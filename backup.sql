


SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pg_net" WITH SCHEMA "extensions";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE OR REPLACE FUNCTION "public"."create_profile"("uid" "uuid", "email" "text", "full_name" "text", "avatar_url" "text", "phone" "text") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
begin
  insert into public.users (id, email, full_name, avatar_url, phone)
  values (uid, email, full_name, avatar_url, phone)
  on conflict (id) do update
    set email = excluded.email,
        full_name = excluded.full_name,
        avatar_url = excluded.avatar_url,
        phone = excluded.phone,
        updated_at = now();
end;
$$;


ALTER FUNCTION "public"."create_profile"("uid" "uuid", "email" "text", "full_name" "text", "avatar_url" "text", "phone" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."expire_old_invitations"() RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    UPDATE business_invitations 
    SET status = 'expired', updated_at = NOW()
    WHERE status = 'pending' AND expires_at < NOW();
END;
$$;


ALTER FUNCTION "public"."expire_old_invitations"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."generate_invitation_token"() RETURNS "text"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN encode(gen_random_bytes(32), 'base64');
END;
$$;


ALTER FUNCTION "public"."generate_invitation_token"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_invitation_token"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    IF NEW.invitation_token IS NULL OR NEW.invitation_token = '' THEN
        NEW.invitation_token := generate_invitation_token();
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."set_invitation_token"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_cashbook_stats"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Update balance and transaction count for new transaction
        UPDATE cashbooks 
        SET 
            balance = balance + CASE 
                WHEN NEW.type = 'income' THEN NEW.amount 
                ELSE -NEW.amount 
            END,
            transaction_count = transaction_count + 1,
            last_activity = NOW()
        WHERE id = NEW.cashbook_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        -- Adjust balance for updated transaction
        UPDATE cashbooks 
        SET 
            balance = balance - CASE 
                WHEN OLD.type = 'income' THEN OLD.amount 
                ELSE -OLD.amount 
            END + CASE 
                WHEN NEW.type = 'income' THEN NEW.amount 
                ELSE -NEW.amount 
            END,
            last_activity = NOW()
        WHERE id = NEW.cashbook_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Adjust balance and count for deleted transaction
        UPDATE cashbooks 
        SET 
            balance = balance - CASE 
                WHEN OLD.type = 'income' THEN OLD.amount 
                ELSE -OLD.amount 
            END,
            transaction_count = transaction_count - 1,
            last_activity = NOW()
        WHERE id = OLD.cashbook_id;
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."update_cashbook_stats"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_updated_at_column"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."budgets" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "cashbook_id" "uuid",
    "category_id" "uuid",
    "name" character varying(255) NOT NULL,
    "amount" numeric(15,2) NOT NULL,
    "spent_amount" numeric(15,2) DEFAULT 0.00,
    "period" character varying(20) DEFAULT 'monthly'::character varying,
    "start_date" "date" NOT NULL,
    "end_date" "date" NOT NULL,
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "budgets_period_check" CHECK ((("period")::"text" = ANY ((ARRAY['weekly'::character varying, 'monthly'::character varying, 'quarterly'::character varying, 'yearly'::character varying])::"text"[])))
);


ALTER TABLE "public"."budgets" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."business_invitations" (
    "business_id" "uuid",
    "email" character varying NOT NULL,
    "invited_by" "uuid",
    "invitation_token" character varying NOT NULL,
    "accepted_at" timestamp with time zone,
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "role" character varying DEFAULT 'staff'::character varying,
    "status" character varying DEFAULT 'pending'::character varying,
    "expires_at" timestamp with time zone DEFAULT ("now"() + '7 days'::interval),
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "business_invitations_role_check" CHECK ((("role")::"text" = ANY (ARRAY[('partner'::character varying)::"text", ('admin'::character varying)::"text", ('staff'::character varying)::"text"]))),
    CONSTRAINT "business_invitations_status_check" CHECK ((("status")::"text" = ANY (ARRAY[('pending'::character varying)::"text", ('accepted'::character varying)::"text", ('declined'::character varying)::"text", ('expired'::character varying)::"text"])))
);


ALTER TABLE "public"."business_invitations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."business_members" (
    "business_id" "uuid",
    "user_id" "uuid",
    "invited_by" "uuid",
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "role" character varying DEFAULT 'staff'::character varying,
    "joined_at" timestamp with time zone DEFAULT "now"(),
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "business_members_role_check" CHECK ((("role")::"text" = ANY (ARRAY[('partner'::character varying)::"text", ('admin'::character varying)::"text", ('staff'::character varying)::"text"])))
);


ALTER TABLE "public"."business_members" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."businesses" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid",
    "name" character varying(255) NOT NULL,
    "description" "text",
    "logo_url" "text",
    "address" "text",
    "phone" character varying(20),
    "email" character varying(255),
    "website" character varying(255),
    "tax_id" character varying(100),
    "currency" character varying(3) DEFAULT '''MWK''::character varying'::character varying,
    "is_default" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."businesses" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."cashbook_shares" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "cashbook_id" "uuid",
    "shared_by" "uuid",
    "shared_with" "uuid",
    "permission" character varying(20) DEFAULT 'view'::character varying,
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "cashbook_shares_permission_check" CHECK ((("permission")::"text" = ANY ((ARRAY['view'::character varying, 'edit'::character varying, 'admin'::character varying])::"text"[])))
);


ALTER TABLE "public"."cashbook_shares" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."cashbook_templates" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "icon" character varying(50),
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."cashbook_templates" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."cashbooks" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid",
    "business_id" "uuid",
    "template_id" "uuid",
    "name" character varying(255) NOT NULL,
    "description" "text",
    "currency" character varying(3) DEFAULT '''MWK''::character varying'::character varying,
    "color" "text",
    "icon" character varying(50),
    "balance" numeric(15,2) DEFAULT 0.00,
    "transaction_count" integer DEFAULT 0,
    "is_default" boolean DEFAULT false,
    "is_active" boolean DEFAULT true,
    "last_activity" timestamp with time zone DEFAULT "now"(),
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."cashbooks" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."categories" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" character varying(255) NOT NULL,
    "type" character varying(20) NOT NULL,
    "icon" character varying(50),
    "color" "text",
    "parent_id" "uuid",
    "sort_order" integer DEFAULT 0,
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "categories_type_check" CHECK ((("type")::"text" = ANY ((ARRAY['income'::character varying, 'expense'::character varying])::"text"[])))
);


ALTER TABLE "public"."categories" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."member_cashbook_permissions" (
    "business_member_id" "uuid",
    "cashbook_id" "uuid",
    "granted_by" "uuid",
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "permission" character varying DEFAULT 'read'::character varying,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "user_id" "uuid",
    CONSTRAINT "member_cashbook_permissions_permission_check" CHECK ((("permission")::"text" = ANY (ARRAY[('read'::character varying)::"text", ('write'::character varying)::"text"])))
);


ALTER TABLE "public"."member_cashbook_permissions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."notification_settings" (
    "user_id" "uuid" NOT NULL,
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "local_notifications_enabled" boolean DEFAULT true,
    "push_notifications_enabled" boolean DEFAULT true,
    "email_notifications_enabled" boolean DEFAULT true,
    "transaction_notifications" boolean DEFAULT true,
    "invitation_notifications" boolean DEFAULT true,
    "broadcast_notifications" boolean DEFAULT true,
    "reminder_notifications" boolean DEFAULT true,
    "system_notifications" boolean DEFAULT true,
    "sound_enabled" boolean DEFAULT true,
    "vibration_enabled" boolean DEFAULT true,
    "quiet_hours_enabled" boolean DEFAULT false,
    "quiet_hours_start" time without time zone DEFAULT '22:00:00'::time without time zone,
    "quiet_hours_end" time without time zone DEFAULT '08:00:00'::time without time zone,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."notification_settings" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."notifications" (
    "user_id" "uuid" NOT NULL,
    "cashbook_id" "uuid",
    "type" "text" NOT NULL,
    "title" "text" NOT NULL,
    "message" "text" NOT NULL,
    "action_url" "text",
    "expires_at" timestamp with time zone,
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "priority" "text" DEFAULT 'normal'::"text" NOT NULL,
    "is_read" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "notifications_priority_check" CHECK (("priority" = ANY (ARRAY['low'::"text", 'normal'::"text", 'high'::"text", 'urgent'::"text"]))),
    CONSTRAINT "notifications_type_check" CHECK (("type" = ANY (ARRAY['transaction'::"text", 'invitation'::"text", 'broadcast'::"text", 'reminder'::"text", 'system'::"text"])))
);


ALTER TABLE "public"."notifications" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."payment_modes" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" character varying(255) NOT NULL,
    "icon" character varying(50),
    "color" "text",
    "is_active" boolean DEFAULT true,
    "sort_order" integer DEFAULT 0,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."payment_modes" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."recurring_transactions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "cashbook_id" "uuid",
    "category_id" "uuid",
    "payment_mode_id" "uuid",
    "type" character varying(20) NOT NULL,
    "amount" numeric(15,2) NOT NULL,
    "description" "text" NOT NULL,
    "frequency" character varying(20) NOT NULL,
    "start_date" "date" NOT NULL,
    "end_date" "date",
    "next_occurrence" "date" NOT NULL,
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "recurring_transactions_frequency_check" CHECK ((("frequency")::"text" = ANY ((ARRAY['daily'::character varying, 'weekly'::character varying, 'monthly'::character varying, 'quarterly'::character varying, 'yearly'::character varying])::"text"[]))),
    CONSTRAINT "recurring_transactions_type_check" CHECK ((("type")::"text" = ANY ((ARRAY['income'::character varying, 'expense'::character varying])::"text"[])))
);


ALTER TABLE "public"."recurring_transactions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."transaction_attachments" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "transaction_id" "uuid",
    "file_name" character varying(255) NOT NULL,
    "file_path" "text" NOT NULL,
    "file_url" "text" NOT NULL,
    "file_type" character varying(50),
    "file_size" bigint,
    "mime_type" character varying(100),
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."transaction_attachments" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."transactions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "cashbook_id" "uuid",
    "user_id" "uuid",
    "category_id" "uuid",
    "payment_mode_id" "uuid",
    "type" character varying(20) NOT NULL,
    "amount" numeric(15,2) NOT NULL,
    "description" "text" NOT NULL,
    "notes" "text",
    "reference_number" character varying(100),
    "transaction_date" "date" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "transactions_type_check" CHECK ((("type")::"text" = ANY ((ARRAY['income'::character varying, 'expense'::character varying])::"text"[])))
);


ALTER TABLE "public"."transactions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_preferences" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid",
    "theme" character varying(20) DEFAULT 'light'::character varying,
    "language" character varying(10) DEFAULT 'en'::character varying,
    "default_currency" character varying(3) DEFAULT 'USD'::character varying,
    "date_format" character varying(20) DEFAULT 'dd/MM/yyyy'::character varying,
    "number_format" character varying(20) DEFAULT 'en_US'::character varying,
    "notifications_enabled" boolean DEFAULT true,
    "biometric_enabled" boolean DEFAULT false,
    "auto_backup" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "user_preferences_theme_check" CHECK ((("theme")::"text" = ANY ((ARRAY['light'::character varying, 'dark'::character varying, 'auto'::character varying])::"text"[])))
);


ALTER TABLE "public"."user_preferences" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "email" character varying(255) NOT NULL,
    "full_name" character varying(255),
    "avatar_url" "text",
    "phone" character varying(20),
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."users" OWNER TO "postgres";


ALTER TABLE ONLY "public"."budgets"
    ADD CONSTRAINT "budgets_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."business_invitations"
    ADD CONSTRAINT "business_invitations_invitation_token_key" UNIQUE ("invitation_token");



ALTER TABLE ONLY "public"."business_invitations"
    ADD CONSTRAINT "business_invitations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."business_members"
    ADD CONSTRAINT "business_members_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."businesses"
    ADD CONSTRAINT "businesses_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."cashbook_shares"
    ADD CONSTRAINT "cashbook_shares_cashbook_id_shared_with_key" UNIQUE ("cashbook_id", "shared_with");



ALTER TABLE ONLY "public"."cashbook_shares"
    ADD CONSTRAINT "cashbook_shares_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."cashbook_templates"
    ADD CONSTRAINT "cashbook_templates_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."cashbooks"
    ADD CONSTRAINT "cashbooks_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."categories"
    ADD CONSTRAINT "categories_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."member_cashbook_permissions"
    ADD CONSTRAINT "member_cashbook_permissions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."notification_settings"
    ADD CONSTRAINT "notification_settings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."notification_settings"
    ADD CONSTRAINT "notification_settings_user_id_key" UNIQUE ("user_id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payment_modes"
    ADD CONSTRAINT "payment_modes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."recurring_transactions"
    ADD CONSTRAINT "recurring_transactions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."transaction_attachments"
    ADD CONSTRAINT "transaction_attachments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."transactions"
    ADD CONSTRAINT "transactions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_preferences"
    ADD CONSTRAINT "user_preferences_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_preferences"
    ADD CONSTRAINT "user_preferences_user_id_key" UNIQUE ("user_id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");



CREATE INDEX "idx_business_invitations_business_id" ON "public"."business_invitations" USING "btree" ("business_id");



CREATE INDEX "idx_business_invitations_email" ON "public"."business_invitations" USING "btree" ("email");



CREATE INDEX "idx_business_invitations_status" ON "public"."business_invitations" USING "btree" ("status");



CREATE INDEX "idx_business_invitations_token" ON "public"."business_invitations" USING "btree" ("invitation_token");



CREATE INDEX "idx_business_members_business_id" ON "public"."business_members" USING "btree" ("business_id");



CREATE INDEX "idx_business_members_user_id" ON "public"."business_members" USING "btree" ("user_id");



CREATE INDEX "idx_cashbooks_business_id" ON "public"."cashbooks" USING "btree" ("business_id");



CREATE INDEX "idx_cashbooks_user_id" ON "public"."cashbooks" USING "btree" ("user_id");



CREATE INDEX "idx_categories_type" ON "public"."categories" USING "btree" ("type");



CREATE INDEX "idx_member_permissions_cashbook_id" ON "public"."member_cashbook_permissions" USING "btree" ("cashbook_id");



CREATE INDEX "idx_member_permissions_member_id" ON "public"."member_cashbook_permissions" USING "btree" ("business_member_id");



CREATE INDEX "idx_transaction_attachments_transaction_id" ON "public"."transaction_attachments" USING "btree" ("transaction_id");



CREATE INDEX "idx_transactions_cashbook_id" ON "public"."transactions" USING "btree" ("cashbook_id");



CREATE INDEX "idx_transactions_date" ON "public"."transactions" USING "btree" ("transaction_date");



CREATE INDEX "idx_transactions_type" ON "public"."transactions" USING "btree" ("type");



CREATE INDEX "idx_transactions_user_id" ON "public"."transactions" USING "btree" ("user_id");



CREATE OR REPLACE TRIGGER "trigger_budgets_updated_at" BEFORE UPDATE ON "public"."budgets" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "trigger_businesses_updated_at" BEFORE UPDATE ON "public"."businesses" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "trigger_cashbooks_updated_at" BEFORE UPDATE ON "public"."cashbooks" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "trigger_recurring_transactions_updated_at" BEFORE UPDATE ON "public"."recurring_transactions" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "trigger_set_invitation_token" BEFORE INSERT ON "public"."business_invitations" FOR EACH ROW EXECUTE FUNCTION "public"."set_invitation_token"();



CREATE OR REPLACE TRIGGER "trigger_transactions_updated_at" BEFORE UPDATE ON "public"."transactions" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "trigger_update_cashbook_stats" AFTER INSERT OR DELETE OR UPDATE ON "public"."transactions" FOR EACH ROW EXECUTE FUNCTION "public"."update_cashbook_stats"();



CREATE OR REPLACE TRIGGER "trigger_user_preferences_updated_at" BEFORE UPDATE ON "public"."user_preferences" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "trigger_users_updated_at" BEFORE UPDATE ON "public"."users" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



ALTER TABLE ONLY "public"."budgets"
    ADD CONSTRAINT "budgets_cashbook_id_fkey" FOREIGN KEY ("cashbook_id") REFERENCES "public"."cashbooks"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."budgets"
    ADD CONSTRAINT "budgets_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."business_invitations"
    ADD CONSTRAINT "business_invitations_business_id_fkey" FOREIGN KEY ("business_id") REFERENCES "public"."businesses"("id");



ALTER TABLE ONLY "public"."business_invitations"
    ADD CONSTRAINT "business_invitations_invited_by_fkey" FOREIGN KEY ("invited_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."business_members"
    ADD CONSTRAINT "business_members_business_id_fkey" FOREIGN KEY ("business_id") REFERENCES "public"."businesses"("id");



ALTER TABLE ONLY "public"."business_members"
    ADD CONSTRAINT "business_members_invited_by_fkey" FOREIGN KEY ("invited_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."business_members"
    ADD CONSTRAINT "business_members_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."businesses"
    ADD CONSTRAINT "businesses_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."cashbook_shares"
    ADD CONSTRAINT "cashbook_shares_cashbook_id_fkey" FOREIGN KEY ("cashbook_id") REFERENCES "public"."cashbooks"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."cashbook_shares"
    ADD CONSTRAINT "cashbook_shares_shared_by_fkey" FOREIGN KEY ("shared_by") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."cashbook_shares"
    ADD CONSTRAINT "cashbook_shares_shared_with_fkey" FOREIGN KEY ("shared_with") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."cashbooks"
    ADD CONSTRAINT "cashbooks_business_id_fkey" FOREIGN KEY ("business_id") REFERENCES "public"."businesses"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."cashbooks"
    ADD CONSTRAINT "cashbooks_template_id_fkey" FOREIGN KEY ("template_id") REFERENCES "public"."cashbook_templates"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."cashbooks"
    ADD CONSTRAINT "cashbooks_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."categories"
    ADD CONSTRAINT "categories_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "public"."categories"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."member_cashbook_permissions"
    ADD CONSTRAINT "member_cashbook_permissions_business_member_id_fkey" FOREIGN KEY ("business_member_id") REFERENCES "public"."business_members"("id");



ALTER TABLE ONLY "public"."member_cashbook_permissions"
    ADD CONSTRAINT "member_cashbook_permissions_cashbook_id_fkey" FOREIGN KEY ("cashbook_id") REFERENCES "public"."cashbooks"("id");



ALTER TABLE ONLY "public"."member_cashbook_permissions"
    ADD CONSTRAINT "member_cashbook_permissions_granted_by_fkey" FOREIGN KEY ("granted_by") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."member_cashbook_permissions"
    ADD CONSTRAINT "member_cashbook_permissions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."notification_settings"
    ADD CONSTRAINT "notification_settings_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_cashbook_id_fkey" FOREIGN KEY ("cashbook_id") REFERENCES "public"."cashbooks"("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_user_id_fkey1" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."recurring_transactions"
    ADD CONSTRAINT "recurring_transactions_cashbook_id_fkey" FOREIGN KEY ("cashbook_id") REFERENCES "public"."cashbooks"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."recurring_transactions"
    ADD CONSTRAINT "recurring_transactions_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."recurring_transactions"
    ADD CONSTRAINT "recurring_transactions_payment_mode_id_fkey" FOREIGN KEY ("payment_mode_id") REFERENCES "public"."payment_modes"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."transaction_attachments"
    ADD CONSTRAINT "transaction_attachments_transaction_id_fkey" FOREIGN KEY ("transaction_id") REFERENCES "public"."transactions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."transactions"
    ADD CONSTRAINT "transactions_cashbook_id_fkey" FOREIGN KEY ("cashbook_id") REFERENCES "public"."cashbooks"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."transactions"
    ADD CONSTRAINT "transactions_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."transactions"
    ADD CONSTRAINT "transactions_payment_mode_id_fkey" FOREIGN KEY ("payment_mode_id") REFERENCES "public"."payment_modes"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."transactions"
    ADD CONSTRAINT "transactions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_preferences"
    ADD CONSTRAINT "user_preferences_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



CREATE POLICY "Anyone can view categories" ON "public"."categories" FOR SELECT USING (true);



CREATE POLICY "Anyone can view payment modes" ON "public"."payment_modes" FOR SELECT USING (true);



CREATE POLICY "Anyone can view templates" ON "public"."cashbook_templates" FOR SELECT USING (true);



CREATE POLICY "Users can delete own budgets" ON "public"."budgets" FOR DELETE USING ((EXISTS ( SELECT 1
   FROM "public"."cashbooks"
  WHERE (("cashbooks"."id" = "budgets"."cashbook_id") AND ("cashbooks"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can delete own businesses" ON "public"."businesses" FOR DELETE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can delete own cashbooks" ON "public"."cashbooks" FOR DELETE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can delete own recurring transactions" ON "public"."recurring_transactions" FOR DELETE USING ((EXISTS ( SELECT 1
   FROM "public"."cashbooks"
  WHERE (("cashbooks"."id" = "recurring_transactions"."cashbook_id") AND ("cashbooks"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can delete own transaction attachments" ON "public"."transaction_attachments" FOR DELETE USING ((EXISTS ( SELECT 1
   FROM "public"."transactions"
  WHERE (("transactions"."id" = "transaction_attachments"."transaction_id") AND ("transactions"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can delete own transactions" ON "public"."transactions" FOR DELETE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can insert own budgets" ON "public"."budgets" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."cashbooks"
  WHERE (("cashbooks"."id" = "budgets"."cashbook_id") AND ("cashbooks"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can insert own businesses" ON "public"."businesses" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can insert own cashbooks" ON "public"."cashbooks" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can insert own preferences" ON "public"."user_preferences" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can insert own recurring transactions" ON "public"."recurring_transactions" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."cashbooks"
  WHERE (("cashbooks"."id" = "recurring_transactions"."cashbook_id") AND ("cashbooks"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can insert own transaction attachments" ON "public"."transaction_attachments" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."transactions"
  WHERE (("transactions"."id" = "transaction_attachments"."transaction_id") AND ("transactions"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can insert own transactions" ON "public"."transactions" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update own budgets" ON "public"."budgets" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."cashbooks"
  WHERE (("cashbooks"."id" = "budgets"."cashbook_id") AND ("cashbooks"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can update own businesses" ON "public"."businesses" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update own cashbooks" ON "public"."cashbooks" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update own preferences" ON "public"."user_preferences" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update own profile" ON "public"."users" FOR UPDATE USING (("auth"."uid"() = "id"));



CREATE POLICY "Users can update own recurring transactions" ON "public"."recurring_transactions" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."cashbooks"
  WHERE (("cashbooks"."id" = "recurring_transactions"."cashbook_id") AND ("cashbooks"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can update own transaction attachments" ON "public"."transaction_attachments" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."transactions"
  WHERE (("transactions"."id" = "transaction_attachments"."transaction_id") AND ("transactions"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can update own transactions" ON "public"."transactions" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view own budgets" ON "public"."budgets" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."cashbooks"
  WHERE (("cashbooks"."id" = "budgets"."cashbook_id") AND ("cashbooks"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can view own businesses" ON "public"."businesses" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view own cashbooks" ON "public"."cashbooks" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view own preferences" ON "public"."user_preferences" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view own profile" ON "public"."users" FOR SELECT USING (("auth"."uid"() = "id"));



CREATE POLICY "Users can view own recurring transactions" ON "public"."recurring_transactions" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."cashbooks"
  WHERE (("cashbooks"."id" = "recurring_transactions"."cashbook_id") AND ("cashbooks"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can view own transaction attachments" ON "public"."transaction_attachments" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."transactions"
  WHERE (("transactions"."id" = "transaction_attachments"."transaction_id") AND ("transactions"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can view own transactions" ON "public"."transactions" FOR SELECT USING (("auth"."uid"() = "user_id"));



ALTER TABLE "public"."cashbook_shares" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";









GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";































































































































































GRANT ALL ON FUNCTION "public"."create_profile"("uid" "uuid", "email" "text", "full_name" "text", "avatar_url" "text", "phone" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."create_profile"("uid" "uuid", "email" "text", "full_name" "text", "avatar_url" "text", "phone" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_profile"("uid" "uuid", "email" "text", "full_name" "text", "avatar_url" "text", "phone" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."expire_old_invitations"() TO "anon";
GRANT ALL ON FUNCTION "public"."expire_old_invitations"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."expire_old_invitations"() TO "service_role";



GRANT ALL ON FUNCTION "public"."generate_invitation_token"() TO "anon";
GRANT ALL ON FUNCTION "public"."generate_invitation_token"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."generate_invitation_token"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_invitation_token"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_invitation_token"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_invitation_token"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_cashbook_stats"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_cashbook_stats"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_cashbook_stats"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "service_role";


















GRANT ALL ON TABLE "public"."budgets" TO "anon";
GRANT ALL ON TABLE "public"."budgets" TO "authenticated";
GRANT ALL ON TABLE "public"."budgets" TO "service_role";



GRANT ALL ON TABLE "public"."business_invitations" TO "anon";
GRANT ALL ON TABLE "public"."business_invitations" TO "authenticated";
GRANT ALL ON TABLE "public"."business_invitations" TO "service_role";



GRANT ALL ON TABLE "public"."business_members" TO "anon";
GRANT ALL ON TABLE "public"."business_members" TO "authenticated";
GRANT ALL ON TABLE "public"."business_members" TO "service_role";



GRANT ALL ON TABLE "public"."businesses" TO "anon";
GRANT ALL ON TABLE "public"."businesses" TO "authenticated";
GRANT ALL ON TABLE "public"."businesses" TO "service_role";



GRANT ALL ON TABLE "public"."cashbook_shares" TO "anon";
GRANT ALL ON TABLE "public"."cashbook_shares" TO "authenticated";
GRANT ALL ON TABLE "public"."cashbook_shares" TO "service_role";



GRANT ALL ON TABLE "public"."cashbook_templates" TO "anon";
GRANT ALL ON TABLE "public"."cashbook_templates" TO "authenticated";
GRANT ALL ON TABLE "public"."cashbook_templates" TO "service_role";



GRANT ALL ON TABLE "public"."cashbooks" TO "anon";
GRANT ALL ON TABLE "public"."cashbooks" TO "authenticated";
GRANT ALL ON TABLE "public"."cashbooks" TO "service_role";



GRANT ALL ON TABLE "public"."categories" TO "anon";
GRANT ALL ON TABLE "public"."categories" TO "authenticated";
GRANT ALL ON TABLE "public"."categories" TO "service_role";



GRANT ALL ON TABLE "public"."member_cashbook_permissions" TO "anon";
GRANT ALL ON TABLE "public"."member_cashbook_permissions" TO "authenticated";
GRANT ALL ON TABLE "public"."member_cashbook_permissions" TO "service_role";



GRANT ALL ON TABLE "public"."notification_settings" TO "anon";
GRANT ALL ON TABLE "public"."notification_settings" TO "authenticated";
GRANT ALL ON TABLE "public"."notification_settings" TO "service_role";



GRANT ALL ON TABLE "public"."notifications" TO "anon";
GRANT ALL ON TABLE "public"."notifications" TO "authenticated";
GRANT ALL ON TABLE "public"."notifications" TO "service_role";



GRANT ALL ON TABLE "public"."payment_modes" TO "anon";
GRANT ALL ON TABLE "public"."payment_modes" TO "authenticated";
GRANT ALL ON TABLE "public"."payment_modes" TO "service_role";



GRANT ALL ON TABLE "public"."recurring_transactions" TO "anon";
GRANT ALL ON TABLE "public"."recurring_transactions" TO "authenticated";
GRANT ALL ON TABLE "public"."recurring_transactions" TO "service_role";



GRANT ALL ON TABLE "public"."transaction_attachments" TO "anon";
GRANT ALL ON TABLE "public"."transaction_attachments" TO "authenticated";
GRANT ALL ON TABLE "public"."transaction_attachments" TO "service_role";



GRANT ALL ON TABLE "public"."transactions" TO "anon";
GRANT ALL ON TABLE "public"."transactions" TO "authenticated";
GRANT ALL ON TABLE "public"."transactions" TO "service_role";



GRANT ALL ON TABLE "public"."user_preferences" TO "anon";
GRANT ALL ON TABLE "public"."user_preferences" TO "authenticated";
GRANT ALL ON TABLE "public"."user_preferences" TO "service_role";



GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "service_role";































RESET ALL;
