import 'business_member_model.dart';

enum InvitationStatus {
  pending,
  accepted,
  declined,
  expired;

  String get displayName {
    switch (this) {
      case InvitationStatus.pending:
        return 'Pending';
      case InvitationStatus.accepted:
        return 'Accepted';
      case InvitationStatus.declined:
        return 'Declined';
      case InvitationStatus.expired:
        return 'Expired';
    }
  }

  static InvitationStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return InvitationStatus.pending;
      case 'accepted':
        return InvitationStatus.accepted;
      case 'declined':
        return InvitationStatus.declined;
      case 'expired':
        return InvitationStatus.expired;
      default:
        return InvitationStatus.pending;
    }
  }
}

class BusinessInvitationModel {
  final String id;
  final String businessId;
  final String email;
  final MemberRole role;
  final String? invitedBy;
  final String invitationToken;
  final InvitationStatus status;
  final DateTime expiresAt;
  final DateTime? acceptedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Additional business info (populated via join)
  final String? businessName;
  final String? inviterName;

  BusinessInvitationModel({
    required this.id,
    required this.businessId,
    required this.email,
    required this.role,
    required this.invitedBy,
    required this.invitationToken,
    required this.status,
    required this.expiresAt,
    this.acceptedAt,
    required this.createdAt,
    required this.updatedAt,
    this.businessName,
    this.inviterName,
  });

  factory BusinessInvitationModel.fromJson(Map<String, dynamic> json) {
    return BusinessInvitationModel(
      id: json['id'] as String,
      businessId: json['business_id'] as String,
      email: json['email'] as String,
      role: MemberRole.fromString(json['role'] as String),
      invitedBy: (json['invited_by'] != null)
          ? (json['invited_by'] is Map<String, dynamic>)
              ? json['invited_by']['email'] as String
              : json['invited_by'] as String
          : null,
      invitationToken: json['invitation_token'] as String,
      status: InvitationStatus.fromString(json['status'] as String),
      expiresAt: DateTime.parse(json['expires_at'] as String),
      acceptedAt: json['accepted_at'] != null
          ? DateTime.parse(json['accepted_at'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      businessName: (json['business'] != null)
          ? json['business'] is String
              ? json['business'] as String
              : json['business']['name'] as String
          : null,
      inviterName: (json.containsKey('users'))
          ? (json['users'] is Map<String, dynamic>)
              ? json['users']['full_name'] as String
              : 'Unknown'
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'business_id': businessId,
      'email': email,
      'role': role.name,
      'invited_by': invitedBy,
      'invitation_token': invitationToken,
      'status': status.name,
      'expires_at': expiresAt.toIso8601String(),
      'accepted_at': acceptedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'business_name': businessName,
      'inviter_name': inviterName,
    };
  }

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get isPending => status == InvitationStatus.pending && !isExpired;
  bool get canBeAccepted => isPending;

  BusinessInvitationModel copyWith({
    String? id,
    String? businessId,
    String? email,
    MemberRole? role,
    String? invitedBy,
    String? invitationToken,
    InvitationStatus? status,
    DateTime? expiresAt,
    DateTime? acceptedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? businessName,
    String? inviterName,
  }) {
    return BusinessInvitationModel(
      id: id ?? this.id,
      businessId: businessId ?? this.businessId,
      email: email ?? this.email,
      role: role ?? this.role,
      invitedBy: invitedBy ?? this.invitedBy,
      invitationToken: invitationToken ?? this.invitationToken,
      status: status ?? this.status,
      expiresAt: expiresAt ?? this.expiresAt,
      acceptedAt: acceptedAt ?? this.acceptedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      businessName: businessName ?? this.businessName,
      inviterName: inviterName ?? this.inviterName,
    );
  }
}
