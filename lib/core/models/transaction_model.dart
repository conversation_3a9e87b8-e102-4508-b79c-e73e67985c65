enum TransactionType { income, expense }

class TransactionModel {
  final String id;
  final String cashbookId;
  final String userId;
  final String? categoryId;
  final String? paymentModeId;
  final TransactionType type;
  final double amount;
  final String description;
  final String? notes;
  final String? referenceNumber;
  final DateTime transactionDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? fullName;

  TransactionModel({
    required this.id,
    required this.cashbookId,
    required this.userId,
    this.categoryId,
    this.paymentModeId,
    required this.type,
    required this.amount,
    required this.description,
    this.notes,
    this.referenceNumber,
    required this.transactionDate,
    required this.createdAt,
    required this.updatedAt,
    this.fullName,
  });

  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    return TransactionModel(
      id: json['id'] as String,
      cashbookId: json['cashbook_id'] as String,
      userId: json['user_id'] as String,
      categoryId: json['category_id'] as String?,
      paymentModeId: json['payment_mode_id'] as String?,
      type: TransactionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => TransactionType.expense,
      ),
      amount: (json['amount'] as num).toDouble(),
      description: json['description'] as String,
      notes: json['notes'] as String?,
      referenceNumber: json['reference_number'] as String?,
      transactionDate: DateTime.parse(json['transaction_date'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      fullName: json['users']?['full_name'] ?? 'N/A' as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'cashbook_id': cashbookId,
      'user_id': userId,
      'category_id': categoryId,
      'payment_mode_id': paymentModeId,
      'type': type.name,
      'amount': amount,
      'description': description,
      'notes': notes,
      'reference_number': referenceNumber,
      'transaction_date': transactionDate.toIso8601String().split('T')[0],
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  TransactionModel copyWith({
    String? id,
    String? cashbookId,
    String? userId,
    String? categoryId,
    String? paymentModeId,
    TransactionType? type,
    double? amount,
    String? description,
    String? notes,
    String? referenceNumber,
    DateTime? transactionDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TransactionModel(
      id: id ?? this.id,
      cashbookId: cashbookId ?? this.cashbookId,
      userId: userId ?? this.userId,
      categoryId: categoryId ?? this.categoryId,
      paymentModeId: paymentModeId ?? this.paymentModeId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      notes: notes ?? this.notes,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      transactionDate: transactionDate ?? this.transactionDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
