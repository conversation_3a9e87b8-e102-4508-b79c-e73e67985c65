import '../models/models.dart';
import 'base_repository.dart';
import '../services/auth_service.dart';

class BusinessMemberRepository extends BaseRepository {
  static const String _membersTable = 'business_members';
  static const String _invitationsTable = 'business_invitations';
  static const String _permissionsTable = 'member_cashbook_permissions';

  /// Generate a simple UUID-like string
  // String _generateUuid() {
  //   final random = Random();
  //   const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  //   return List.generate(32, (index) => chars[random.nextInt(chars.length)])
  //       .join();
  // }

  /// Get current user from auth service
  String? get _currentUserEmail => AuthService.instance.currentUser?.email;

  /// Get all members for a business
  Future<List<BusinessMemberModel>> getBusinessMembers(
      String businessId) async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_membersTable)
          .select('''
            *,
            users!business_members_user_id_fkey!inner(
              id,
              email,
              full_name,
              avatar_url
            )
          ''')
          .eq('business_id', businessId)
          .eq('is_active', true)
          .order('created_at', ascending: false);

      // Deduplicate by user_id (keep first = most recent due to ordering)
      final seenUserIds = <String>{};
      final uniqueMembers = <dynamic>[];

      for (final member in response) {
        final userId = member['users']['id'];
        if (!seenUserIds.contains(userId)) {
          seenUserIds.add(userId);
          uniqueMembers.add(member);
        }
      }

      return uniqueMembers.map((json) {
        final userData = json['users'] as Map<String, dynamic>?;
        return BusinessMemberModel.fromJson({
          ...json,
          'user_email': userData?['email'],
          'user_full_name': userData?['full_name'],
          'user_avatar_url': userData?['avatar_url'],
        });
      }).toList();
    });
  }

  /// Get member by user ID and business ID
  Future<BusinessMemberModel?> getMemberByUserAndBusiness(
    String userId,
    String businessId,
  ) async {
    requireAuth();
    return executeQuery(() async {
      final response = await client
          .from(_membersTable)
          .select('''
            *,
            users!business_members_user_id_fkey!inner(
              email,
              full_name,
              avatar_url
            )
          ''')
          .eq('user_id', userId)
          .eq('business_id', businessId)
          .eq('is_active', true)
          .limit(1);

      Map<String, dynamic>? userData = {};
      if (response.first.containsKey('users')) {
        userData = response.first['users'];
      }

      return BusinessMemberModel.fromJson({
        ...response.first,
        'user_email': userData?['email'],
        'user_full_name': userData?['full_name'],
        'user_avatar_url': userData?['avatar_url'],
      });
    });
  }

  /// Add a member to business (after invitation acceptance)
  Future<BusinessMemberModel> addMember({
    required String businessId,
    required String userId,
    required MemberRole role,
    String? invitedBy,
  }) async {
    requireAuth();

    return executeQuery(() async {
      final memberData = {
        'business_id': businessId,
        'user_id': userId,
        'role': role.name,
        'invited_by': invitedBy,
        'joined_at': DateTime.now().toIso8601String(),
        'is_active': true,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response =
          await client.from(_membersTable).insert(memberData).select('''
            *,
            users!business_members_user_id_fkey(
              email,
              full_name,
              avatar_url
            )
          ''').single();

      final userData = response['users'] as Map<String, dynamic>?;
      return BusinessMemberModel.fromJson({
        ...response,
        'user_email': userData?['email'],
        'user_full_name': userData?['full_name'],
        'user_avatar_url': userData?['avatar_url'],
      });
    });
  }

  /// Update member role
  Future<BusinessMemberModel> updateMemberRole(
    String memberId,
    MemberRole newRole,
  ) async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_membersTable)
          .update({
            'role': newRole.name,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', memberId)
          .select('''
            *,
            users!business_members_user_id_fkey(
              email,
              full_name,
              avatar_url
            )
          ''')
          .single();

      final userData = response['users'] as Map<String, dynamic>?;
      return BusinessMemberModel.fromJson({
        ...response,
        'user_email': userData?['email'],
        'user_full_name': userData?['full_name'],
        'user_avatar_url': userData?['avatar_url'],
      });
    });
  }

  /// Remove member from business
  Future<void> removeMember(String memberId) async {
    requireAuth();

    return executeQuery(() async {
      await client.from(_membersTable).update({
        'is_active': false,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', memberId);
    });
  }

  /// Send invitation to join business
  Future<BusinessInvitationModel> sendInvitation({
    required String businessId,
    required String email,
    required MemberRole role,
  }) async {
    requireAuth();

    return executeQuery(() async {
      // Check if invitation already exists
      final existingInvitation = await client
          .from(_invitationsTable)
          .select()
          .eq('business_id', businessId)
          .eq('email', email)
          .eq('status', 'pending')
          .maybeSingle();

      if (existingInvitation != null) {
        throw Exception(
            'An invitation has already been sent to this email address');
      }

      final invitationData = {
        'business_id': businessId,
        'email': email,
        'role': role.name,
        'invited_by': currentUserId!,
        'status': 'pending'
      };

      final response =
          await client.from(_invitationsTable).insert(invitationData).select('''
            *,
            businesses!business_invitations_business_id_fkey(name),
            users!business_invitations_invited_by_fkey(full_name)
          ''').single();

      final businessData = response['businesses'] as Map<String, dynamic>?;
      final inviterData = response['users'] as Map<String, dynamic>?;

      return BusinessInvitationModel.fromJson({
        ...response,
        'business_name': businessData?['name'],
        'inviter_name': inviterData?['full_name'],
      });
    });
  }

  /// Get invitations for a business
  Future<List<BusinessInvitationModel>> getBusinessInvitations(
      String businessId) async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_invitationsTable)
          .select('''
            *,
            businesses!business_invitations_business_id_fkey(name),
            users!business_invitations_invited_by_fkey(full_name)
          ''')
          .eq('business_id', businessId)
          .order('created_at', ascending: false);

      return response.map((json) {
        final businessData = json['businesses'] as Map<String, dynamic>?;
        final inviterData = json['users'] as Map<String, dynamic>?;
        return BusinessInvitationModel.fromJson({
          ...json,
          'business_name': businessData?['name'],
          'inviter_name': inviterData?['full_name'],
        });
      }).toList();
    });
  }

  /// Get invitation by token
  Future<BusinessInvitationModel?> getInvitationByToken(String token) async {
    return executeQuery(() async {
      final response = await client.from(_invitationsTable).select('''
            *,
            businesses!business_invitations_business_id_fkey(name),
            users!business_invitations_invited_by_fkey(full_name)
          ''').eq('invitation_token', token).maybeSingle();

      if (response == null) return null;

      final businessData = response['businesses'] as Map<String, dynamic>?;
      final inviterData = response['users'] as Map<String, dynamic>?;

      return BusinessInvitationModel.fromJson({
        ...response,
        'business_name': businessData?['name'],
        'inviter_name': inviterData?['full_name'],
      });
    });
  }

  /// Accept invitation
  Future<BusinessMemberModel> acceptInvitation(String invitationId) async {
    requireAuth();

    return executeQuery(() async {
      // Get the invitation
      final invitation = await client
          .from(_invitationsTable)
          .select()
          .eq('id', invitationId)
          .single();

      final invitationModel = BusinessInvitationModel.fromJson(invitation);

      if (!invitationModel.canBeAccepted) {
        throw Exception('This invitation cannot be accepted');
      }

      // Update invitation status
      await client.from(_invitationsTable).update({
        'status': 'accepted',
        'accepted_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', invitationId);

      // Add member to business
      return await addMember(
        businessId: invitationModel.businessId,
        userId: currentUserId!,
        role: invitationModel.role,
        invitedBy: invitationModel.invitedBy,
      );
    });
  }

  /// Decline invitation
  Future<void> declineInvitation(String invitationId) async {
    return executeQuery(() async {
      await client.from(_invitationsTable).update({
        'status': 'declined',
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', invitationId);
    });
  }

  /// Cancel invitation
  Future<void> cancelInvitation(String invitationId) async {
    requireAuth();

    return executeQuery(() async {
      await client.from(_invitationsTable).delete().eq('id', invitationId);
    });
  }

  /// Grant cashbook permission to member
  Future<MemberCashbookPermissionModel> grantCashbookPermission({
    required String businessMemberId,
    required String cashbookId,
    required CashbookPermission permission,
  }) async {
    requireAuth();

    return executeQuery(() async {
      final permissionData = {
        'business_member_id': businessMemberId,
        'cashbook_id': cashbookId,
        'permission': permission.name,
        'granted_by': currentUserId!,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response =
          await client.from(_permissionsTable).upsert(permissionData).select('''
            *,
            business_members!member_cashbook_permissions_business_member_id_fkey(
              users!business_members_user_id_fkey(full_name, email)
            ),
            cashbooks!member_cashbook_permissions_cashbook_id_fkey(name)
          ''').single();

      final memberData = response['business_members'] as Map<String, dynamic>?;
      final userData = memberData?['users'] as Map<String, dynamic>?;
      final cashbookData = response['cashbooks'] as Map<String, dynamic>?;

      return MemberCashbookPermissionModel.fromJson({
        ...response,
        'member_name': userData?['full_name'],
        'member_email': userData?['email'],
        'cashbook_name': cashbookData?['name'],
      });
    });
  }

  /// Get member's cashbook permissions
  Future<List<MemberCashbookPermissionModel>> getMemberCashbookPermissions(
    String businessMemberId,
  ) async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_permissionsTable)
          .select('''
            *,
            business_members!member_cashbook_permissions_business_member_id_fkey(
              users!business_members_user_id_fkey(full_name, email)
            ),
            cashbooks!member_cashbook_permissions_cashbook_id_fkey(name)
          ''')
          .eq('business_member_id', businessMemberId)
          .order('created_at', ascending: false);

      return response.map((json) {
        final memberData = json['business_members'] as Map<String, dynamic>?;
        final userData = memberData?['users'] as Map<String, dynamic>?;
        final cashbookData = json['cashbooks'] as Map<String, dynamic>?;

        return MemberCashbookPermissionModel.fromJson({
          ...json,
          'member_name': userData?['full_name'],
          'member_email': userData?['email'],
          'cashbook_name': cashbookData?['name'],
        });
      }).toList();
    });
  }

  /// Get all permissions for a cashbook
  Future<List<MemberCashbookPermissionModel>> getCashbookPermissions(
    String cashbookId,
  ) async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_permissionsTable)
          .select('''
            *,
            business_members!member_cashbook_permissions_business_member_id_fkey(
              users!business_members_user_id_fkey(full_name, email)
            ),
            cashbooks!member_cashbook_permissions_cashbook_id_fkey(name)
          ''')
          .eq('cashbook_id', cashbookId)
          .order('created_at', ascending: false);

      return response.map((json) {
        final memberData = json['business_members'] as Map<String, dynamic>?;
        final userData = memberData?['users'] as Map<String, dynamic>?;
        final cashbookData = json['cashbooks'] as Map<String, dynamic>?;

        return MemberCashbookPermissionModel.fromJson({
          ...json,
          'member_name': userData?['full_name'],
          'member_email': userData?['email'],
          'cashbook_name': cashbookData?['name'],
        });
      }).toList();
    });
  }

  /// Revoke cashbook permission
  Future<void> revokeCashbookPermission(String permissionId) async {
    requireAuth();

    return executeQuery(() async {
      await client.from(_permissionsTable).delete().eq('id', permissionId);
    });
  }

  /// Update cashbook permission
  Future<MemberCashbookPermissionModel> updateCashbookPermission(
    String permissionId,
    CashbookPermission newPermission,
  ) async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_permissionsTable)
          .update({
            'permission': newPermission.name,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', permissionId)
          .select('''
            *,
            business_members!member_cashbook_permissions_business_member_id_fkey(
              users!business_members_user_id_fkey(full_name, email)
            ),
            cashbooks!member_cashbook_permissions_cashbook_id_fkey(name)
          ''')
          .single();

      final memberData = response['business_members'] as Map<String, dynamic>?;
      final userData = memberData?['users'] as Map<String, dynamic>?;
      final cashbookData = response['cashbooks'] as Map<String, dynamic>?;

      return MemberCashbookPermissionModel.fromJson({
        ...response,
        'member_name': userData?['full_name'],
        'member_email': userData?['email'],
        'cashbook_name': cashbookData?['name'],
      });
    });
  }

  /// Get invitations received by the current user
  Future<List<BusinessInvitationModel>> getReceivedInvitations() async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from('business_invitations')
          .select('''
            *,
            invited_by (
              email,
              full_name
            ),
            business: business_id (
              name
            )
          ''')
          .eq('email', _currentUserEmail!)
          .inFilter('status', ['pending'])
          .order('created_at', ascending: false);

      return response
          .map((json) => BusinessInvitationModel.fromJson(json))
          .toList();
    });
  }

  /// Respond to an invitation (accept or decline)
  Future<void> respondToInvitation(String invitationId, bool accept) async {
    requireAuth();

    // return executeQuery(() async {
    final status = accept ? 'accepted' : 'declined';
    final now = DateTime.now().toIso8601String();

    // Update invitation status
    await client
        .from('business_invitations')
        .update({
          'status': status,
          'accepted_at': accept ? now : null,
          'updated_at': now,
        })
        .eq('id', invitationId)
        .eq('email', _currentUserEmail!);

    // If accepted, create business member record
    if (accept) {
      // Get invitation details
      final invitationResponse = await client
          .from('business_invitations')
          .select('business_id, role, invited_by')
          .eq('id', invitationId)
          .single();

      var exists = await client
          .from('business_members')
          .select()
          .eq('business_id', invitationResponse['business_id'])
          .eq('user_id', currentUserId!)
          .maybeSingle();

      if (exists == null) {
        // Create business member
        await client.from('business_members').insert({
          'business_id': invitationResponse['business_id'],
          'user_id': currentUserId,
          'role': invitationResponse['role'],
          'joined_at': now,
          'created_at': now,
          'updated_at': now,
        });
      }

      // Get all cashbooks for the business
      final cashbooksResponse = await client
          .from('cashbooks')
          .select('id')
          .eq('business_id', invitationResponse['business_id']);

      final bussinessMember = await client
          .from('business_members')
          .select('id')
          .eq('business_id', invitationResponse['business_id'])
          .eq('user_id', invitationResponse['invited_by'])
          .single();
      // Create business member permissions based on role
      if (invitationResponse['role'] == 'partner') {
        // Create permissions for each cashbook
        for (final cashbook in cashbooksResponse) {
          await client.from('member_cashbook_permissions').insert({
            'business_member_id': bussinessMember['id'],
            'cashbook_id': cashbook['id'],
            'user_id': currentUserId!,
            'permission': 'write',
            'created_at': now,
            'updated_at': now,
          });
        }
      }

      if (invitationResponse['role'] == 'admin') {
        // Create permissions for each cashbook
        for (final cashbook in cashbooksResponse) {
          await client.from('member_cashbook_permissions').insert({
            'business_member_id': currentUserId,
            'cashbook_id': cashbook['id'],
            'permission': 'write',
            'granted_by': currentUserId,
            'created_at': now,
            'updated_at': now,
          });
        }
      }

      if (invitationResponse['role'] == 'staff') {
        // Create permissions for each cashbook
        for (final cashbook in cashbooksResponse) {
          await client.from('member_cashbook_permissions').insert({
            'business_member_id': currentUserId,
            'cashbook_id': cashbook['id'],
            'permission': 'read',
            'granted_by': currentUserId,
            'created_at': now,
            'updated_at': now,
          });
        }
      }
    }
    // });
  }
}
