import '../models/models.dart';
import 'base_repository.dart';

class BusinessRepository extends BaseRepository {
  static const String _tableName = 'businesses';

  /// Get all businesses for current user
  Future<List<BusinessModel>> getUserBusinesses() async {
    return await getInvitedBusinesses();
  }

  // Get user invited businesses
  Future<List<BusinessModel>> getInvitedBusinesses() async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from('businesses')
          .select(
            '''
              *,
              business_members!inner(*)
            ''',
          )
          .eq(
            'business_members.user_id',
            currentUserId!,
          )
          .order('created_at', ascending: false);
      print(response);
      return response.map((json) => BusinessModel.fromJson(json)).toList();
    });
  }

  /// Get business by ID
  Future<BusinessModel?> getBusinessById(String businessId) async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('id', businessId)
          .eq('user_id', currentUserId!)
          .maybeSingle();

      return response != null ? BusinessModel.fromJson(response) : null;
    });
  }

  /// Get default business
  Future<BusinessModel?> getDefaultBusiness() async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_tableName)
          .select()
          .eq('user_id', currentUserId!)
          .eq('is_default', true)
          .maybeSingle();

      return response != null ? BusinessModel.fromJson(response) : null;
    });
  }

  /// Create new business
  Future<BusinessModel> createBusiness({
    required String name,
    String? description,
    String? logoUrl,
    String? address,
    String? phone,
    String? email,
    String? website,
    String? taxId,
    String currency = 'USD',
    bool isDefault = false,
  }) async {
    requireAuth();

    return executeQuery(() async {
      // If this is set as default, unset other defaults first
      if (isDefault) {
        await _unsetDefaultBusinesses();
      }

      final businessData = {
        'user_id': currentUserId!,
        'name': name,
        'description': description,
        'logo_url': logoUrl,
        'address': address,
        'phone': phone,
        'email': email,
        'website': website,
        'tax_id': taxId,
        'currency': currency,
        'is_default': isDefault,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };
      print('creating bussiness ... ');
      final response =
          await client.from(_tableName).insert(businessData).select().single();

      return BusinessModel.fromJson(response);
    });
  }

  /// Update business
  Future<BusinessModel> updateBusiness({
    required String businessId,
    String? name,
    String? description,
    String? logoUrl,
    String? address,
    String? phone,
    String? email,
    String? website,
    String? taxId,
    String? currency,
    bool? isDefault,
  }) async {
    requireAuth();

    return executeQuery(() async {
      // If this is set as default, unset other defaults first
      if (isDefault == true) {
        await _unsetDefaultBusinesses();
      }

      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (name != null) updateData['name'] = name;
      if (description != null) updateData['description'] = description;
      if (logoUrl != null) updateData['logo_url'] = logoUrl;
      if (address != null) updateData['address'] = address;
      if (phone != null) updateData['phone'] = phone;
      if (email != null) updateData['email'] = email;
      if (website != null) updateData['website'] = website;
      if (taxId != null) updateData['tax_id'] = taxId;
      if (currency != null) updateData['currency'] = currency;
      if (isDefault != null) updateData['is_default'] = isDefault;

      final response = await client
          .from(_tableName)
          .update(updateData)
          .eq('id', businessId)
          .eq('user_id', currentUserId!)
          .select()
          .single();

      return BusinessModel.fromJson(response);
    });
  }

  /// Delete business
  Future<void> deleteBusiness(String businessId) async {
    requireAuth();

    return executeQuery(() async {
      await client
          .from(_tableName)
          .delete()
          .eq('id', businessId)
          .eq('user_id', currentUserId!);
    });
  }

  /// Set business as default
  Future<void> setDefaultBusiness(String businessId) async {
    requireAuth();

    return executeQuery(() async {
      // First unset all defaults
      await _unsetDefaultBusinesses();

      // Then set the specified business as default
      await client
          .from(_tableName)
          .update({
            'is_default': true,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', businessId)
          .eq('user_id', currentUserId!);
    });
  }

  /// Helper method to unset all default businesses
  Future<void> _unsetDefaultBusinesses() async {
    await client
        .from(_tableName)
        .update({
          'is_default': false,
          'updated_at': DateTime.now().toIso8601String(),
        })
        .eq('user_id', currentUserId!)
        .eq('is_default', true);
  }
}
