import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import '../models/models.dart';
import '../repositories/repositories.dart';
import 'supabase_service.dart';

class NotificationService {
  static NotificationService? _instance;
  static NotificationService get instance =>
      _instance ??= NotificationService._();

  NotificationService._();

  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  late final NotificationRepository _repository;

  StreamSubscription<List<Map<String, dynamic>>>? _realtimeSubscription;
  final StreamController<NotificationModel> _notificationStreamController =
      StreamController<NotificationModel>.broadcast();
  final StreamController<int> _unreadCountStreamController =
      StreamController<int>.broadcast();

  // Getters for streams
  Stream<NotificationModel> get notificationStream =>
      _notificationStreamController.stream;
  Stream<int> get unreadCountStream => _unreadCountStreamController.stream;

  bool _isInitialized = false;
  NotificationSettings? _currentSettings;

  /// Initialize the notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    _repository = NotificationRepository();

    await _initializeLocalNotifications();
    await _loadNotificationSettings();
    await _setupRealtimeSubscription();

    _isInitialized = true;
  }

  /// Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Request permissions for Android 13+
    if (Platform.isAndroid) {
      await _localNotifications
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();
    }
  }

  /// Load notification settings
  Future<void> _loadNotificationSettings() async {
    if (SupabaseService.instance.currentUserId == null) return;
    try {
      _currentSettings = await _repository.getNotificationSettings();
      _currentSettings ??= await _repository.createDefaultSettings();
    } catch (e) {
      debugPrint('Error loading notification settings: $e');
      // Create default settings if loading fails
      _currentSettings = NotificationSettings(
        userId: SupabaseService.instance.currentUserId!,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }
  }

  /// Setup real-time subscription for notifications
  Future<void> _setupRealtimeSubscription() async {
    final userId = SupabaseService.instance.currentUserId;
    if (userId == null) return;

    try {
      _realtimeSubscription = SupabaseService.instance.client
          .from('notifications')
          .stream(primaryKey: ['id'])
          .eq('user_id', userId)
          .listen((data) {
            for (final item in data) {
              final notification = NotificationModel.fromJson(item);
              _notificationStreamController.add(notification);

              // Show local notification if enabled
              if (_shouldShowLocalNotification(notification)) {
                _showLocalNotification(notification);
              }
            }

            // Update unread count
            _updateUnreadCount();
          });
    } catch (e) {
      debugPrint('Error setting up real-time subscription: $e');
    }
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    debugPrint('Notification tapped: ${response.payload}');
    // TODO: Navigate to appropriate screen based on payload
  }

  /// Show local notification
  Future<void> _showLocalNotification(NotificationModel notification) async {
    if (!_currentSettings!.localNotificationsEnabled) return;
    if (_currentSettings!.isInQuietHours()) return;

    const androidDetails = AndroidNotificationDetails(
      'cashbook_notifications',
      'Cashbook Notifications',
      channelDescription: 'Notifications for cashbook activities',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      notification.id.hashCode,
      notification.title,
      notification.message,
      details,
      payload: notification.actionUrl,
    );
  }

  /// Check if local notification should be shown
  bool _shouldShowLocalNotification(NotificationModel notification) {
    if (!_currentSettings!.localNotificationsEnabled) return false;
    if (!_currentSettings!.isNotificationTypeEnabled(notification.type))
      return false;
    if (_currentSettings!.isInQuietHours()) return false;

    return true;
  }

  /// Update unread count
  Future<void> _updateUnreadCount() async {
    try {
      final count = await _repository.getUnreadCount();
      _unreadCountStreamController.add(count);
    } catch (e) {
      debugPrint('Error updating unread count: $e');
    }
  }

  /// Get notifications
  Future<List<NotificationModel>> getNotifications({
    int limit = 50,
    int offset = 0,
    bool? isRead,
    NotificationType? type,
    String? cashbookId,
  }) async {
    return await _repository.getUserNotifications(
      limit: limit,
      offset: offset,
      isRead: isRead,
      type: type,
      cashbookId: cashbookId,
    );
  }

  /// Get unread count
  Future<int> getUnreadCount() async {
    return await _repository.getUnreadCount();
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    await _repository.markAsRead(notificationId);
    await _updateUnreadCount();
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    await _repository.markAllAsRead();
    await _updateUnreadCount();
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    await _repository.deleteNotification(notificationId);
    await _updateUnreadCount();
  }

  /// Broadcast notification
  Future<void> broadcastNotification(
      BroadcastNotificationRequest request) async {
    await _repository.broadcastNotification(request);
  }

  /// Get notification settings
  NotificationSettings? get currentSettings => _currentSettings;

  /// Update notification settings
  Future<void> updateSettings(NotificationSettings settings) async {
    _currentSettings = await _repository.updateNotificationSettings(settings);
  }

  /// Dispose resources
  void dispose() {
    _realtimeSubscription?.cancel();
    _notificationStreamController.close();
    _unreadCountStreamController.close();
  }
}
