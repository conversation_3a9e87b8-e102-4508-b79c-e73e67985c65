import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class AmountInputField extends StatelessWidget {
  final TextEditingController controller;
  final bool isIncome;
  final String? errorText;
  final ValueChanged<String>? onChanged;
  final String currency;

  const AmountInputField({
    super.key,
    required this.controller,
    required this.isIncome,
    required this.currency,
    this.errorText,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Amount',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        Sized<PERSON><PERSON>(height: 1.h),
        Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(
              color: errorText != null
                  ? AppTheme.getErrorColor(isDark)
                  : (isDark
                      ? const Color(0xFF424242)
                      : const Color(0xFFE0E0E0)),
              width: errorText != null ? 2.0 : 1.0,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: isIncome
                      ? AppTheme.getSuccessColor(isDark).withValues(alpha: 0.1)
                      : AppTheme.getErrorColor(isDark).withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8.0),
                    bottomLeft: Radius.circular(8.0),
                  ),
                ),
                child: Text(
                  currency,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: isIncome
                          ? AppTheme.getSuccessColor(isDark)
                          : AppTheme.getErrorColor(isDark),
                      fontSize: 16.sp),
                ),
              ),
              Expanded(
                child: TextFormField(
                  controller: controller,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                        RegExp(r'^\d*\.?\d{0,2}')),
                  ],
                  onChanged: onChanged,
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(fontSize: 16.sp),
                  decoration: InputDecoration(
                    hintText: '0.00',
                    hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: isDark
                            ? const Color(0xFF616161)
                            : const Color(0xFFBDBDBD),
                        fontSize: 16.sp),
                    border: InputBorder.none,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                  ),
                ),
              ),
            ],
          ),
        ),
        if (errorText != null) ...[
          SizedBox(height: 0.5.h),
          Text(
            errorText!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.getErrorColor(isDark),
                  fontSize: 12.sp,
                ),
          ),
        ],
      ],
    );
  }
}
