import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';
import '../../../widgets/custom_icon_widget.dart';

class CategorySelector extends StatelessWidget {
  final CategoryModel? selectedCategory;
  final bool isIncome;
  final ValueChanged<CategoryModel> onCategorySelected;
  final List<CategoryModel> categories;

  const CategorySelector({
    super.key,
    required this.selectedCategory,
    required this.isIncome,
    required this.onCategorySelected,
    required this.categories,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Category',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        SizedBox(height: 1.h),
        GestureDetector(
          onTap: () => _showCategoryBottomSheet(context, categories),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(
                color:
                    isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0),
                width: 1.0,
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'category',
                  color: isIncome
                      ? AppTheme.getSuccessColor(isDark)
                      : AppTheme.getErrorColor(isDark),
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Expanded(
                  child: Text(
                    selectedCategory?.name ?? 'Select category',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                          color:
                              isDark ? Colors.white : const Color(0xFF212121),
                        ),
                  ),
                ),
                CustomIconWidget(
                  iconName: 'keyboard_arrow_down',
                  color: isDark
                      ? const Color(0xFFB0B0B0)
                      : const Color(0xFF757575),
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _showCategoryBottomSheet(
      BuildContext context, List<CategoryModel> categoriesList) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    final incomeCategories = categoriesList
        .where((category) => category.type == CategoryType.income)
        .toList();

    final expenseCategories = categoriesList
        .where((category) => category.type == CategoryType.expense)
        .toList();

    final List<CategoryModel> categories =
        (isIncome ? incomeCategories : expenseCategories);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 70.h,
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20.0),
            topRight: Radius.circular(20.0),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 12.w,
              height: 0.5.h,
              margin: EdgeInsets.only(top: 1.h),
              decoration: BoxDecoration(
                color:
                    isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0),
                borderRadius: BorderRadius.circular(2.0),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(4.w),
              child: Row(
                children: [
                  Text(
                    'Select ${isIncome ? 'Income' : 'Expense'} Category',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color:
                              isDark ? Colors.white : const Color(0xFF212121),
                        ),
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: CustomIconWidget(
                      iconName: 'close',
                      color: isDark
                          ? const Color(0xFFB0B0B0)
                          : const Color(0xFF757575),
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: GridView.builder(
                padding: EdgeInsets.symmetric(horizontal: 4.w),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 3.5,
                  crossAxisSpacing: 3.w,
                  mainAxisSpacing: 2.h,
                ),
                itemCount: categories.length + 1,
                itemBuilder: (context, index) {
                  if (index == categories.length) {
                    return GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                        _showAddCustomCategoryDialog(context);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: (isIncome
                                  ? AppTheme.getSuccessColor(isDark)
                                  : AppTheme.getErrorColor(isDark))
                              .withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8.0),
                          border: Border.all(
                            color: isIncome
                                ? AppTheme.getSuccessColor(isDark)
                                : AppTheme.getErrorColor(isDark),
                            style: BorderStyle.solid,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomIconWidget(
                              iconName: 'add',
                              color: isIncome
                                  ? AppTheme.getSuccessColor(isDark)
                                  : AppTheme.getErrorColor(isDark),
                              size: 20,
                            ),
                            SizedBox(width: 2.w),
                            Text(
                              'Add Custom',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: isIncome
                                        ? AppTheme.getSuccessColor(isDark)
                                        : AppTheme.getErrorColor(isDark),
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  final category = categories[index];
                  final isSelected = selectedCategory == category.name;

                  return GestureDetector(
                    onTap: () {
                      onCategorySelected(category);
                      Navigator.pop(context);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected
                            ? (isIncome
                                    ? AppTheme.getSuccessColor(isDark)
                                    : AppTheme.getErrorColor(isDark))
                                .withValues(alpha: 0.1)
                            : theme.colorScheme.surface,
                        borderRadius: BorderRadius.circular(8.0),
                        border: Border.all(
                          color: isSelected
                              ? (isIncome
                                  ? AppTheme.getSuccessColor(isDark)
                                  : AppTheme.getErrorColor(isDark))
                              : (isDark
                                  ? const Color(0xFF424242)
                                  : const Color(0xFFE0E0E0)),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomIconWidget(
                            iconName: category.icon.toString(),
                            color: isSelected
                                ? (isIncome
                                    ? AppTheme.getSuccessColor(isDark)
                                    : AppTheme.getErrorColor(isDark))
                                : (isDark
                                    ? const Color(0xFFB0B0B0)
                                    : const Color(0xFF757575)),
                            size: 20,
                          ),
                          SizedBox(width: 2.w),
                          Flexible(
                            child: Text(
                              category.name,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: isDark
                                        ? const Color(0xFFB0B0B0)
                                        : const Color(0xFF757575),
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.w500,
                                  ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddCustomCategoryDialog(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final TextEditingController controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: theme.colorScheme.surface,
        title: Text(
          'Add Custom Category',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : const Color(0xFF212121),
              ),
        ),
        content: TextFormField(
          controller: controller,
          autofocus: true,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: isDark ? Colors.white : const Color(0xFF212121),
              ),
          decoration: InputDecoration(
            hintText: 'Enter category name',
            hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isDark
                      ? const Color(0xFF616161)
                      : const Color(0xFFBDBDBD),
                ),
            fillColor: theme.colorScheme.surface,
            filled: true,
            contentPadding:
                EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(
                color:
                    isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(
                color:
                    isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(
                color:
                    isDark ? const Color(0xFF4A90E2) : const Color(0xFF1B365D),
                width: 2.0,
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: isDark
                        ? const Color(0xFFB0B0B0)
                        : const Color(0xFF757575),
                  ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.trim().isNotEmpty) {
                onCategorySelected(categories
                    .firstWhere((c) => c.name == controller.text.trim()));
                Navigator.pop(context);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: isIncome
                  ? AppTheme.getSuccessColor(isDark)
                  : AppTheme.getErrorColor(isDark),
              foregroundColor: Colors.white,
            ),
            child: Text(
              'Add',
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
