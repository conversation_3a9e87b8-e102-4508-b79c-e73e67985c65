import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/member_card_widget.dart';
import './widgets/invitation_card_widget.dart';
import './widgets/invite_member_bottom_sheet.dart';

class BusinessMembersScreen extends StatefulWidget {
  final String businessId;
  final String businessName;

  const BusinessMembersScreen({
    super.key,
    required this.businessId,
    required this.businessName,
  });

  @override
  State<BusinessMembersScreen> createState() => _BusinessMembersScreenState();
}

class _BusinessMembersScreenState extends State<BusinessMembersScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Data
  List<BusinessMemberModel> _members = [];
  List<BusinessInvitationModel> _invitations = [];
  bool _isLoading = true;
  String? _error;
  bool _canManageMembers = false;

  // Services
  late BusinessMemberService _memberService;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _memberService = BusinessMemberService.instance;
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final results = await Future.wait([
        _memberService.getBusinessMembers(widget.businessId),
        _memberService.getBusinessInvitations(widget.businessId),
        _memberService.canManageMembers(widget.businessId),
      ]);

      setState(() {
        _members = results[0] as List<BusinessMemberModel>;
        _invitations = results[1] as List<BusinessInvitationModel>;
        _canManageMembers = results[2] as bool;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _error = 'Something went wrong. Please try again later.';
        _isLoading = false;
      });
    }
  }

  void _showInviteMemberBottomSheet() {
    // Capture the parent context before showing the bottom sheet
    final scaffoldContext = context;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => InviteMemberBottomSheet(
        businessId: widget.businessId,
        businessName: widget.businessName,
        onInvitationSent: () {
          _loadData(); // Refresh data
          // Use the captured parent context instead of the bottom sheet's context
          if (mounted) {
            ScaffoldMessenger.of(scaffoldContext).showSnackBar(
              const SnackBar(content: Text('Invitation sent successfully')),
            );
          }
        },
      ),
    );
  }

  Future<void> _updateMemberRole(
      BusinessMemberModel member, MemberRole newRole) async {
    if (!mounted) return;

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _buildLoadingDialog('Updating role...'),
    );

    try {
      await _memberService.updateMemberRole(
        member.id,
        newRole,
        widget.businessId,
      );

      // Close loading dialog
      if (mounted) Navigator.pop(context);

      await _loadData();

      // Show success message with enhanced styling
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle,
                    color: Theme.of(context).colorScheme.onPrimary),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    '${member.userFullName ?? member.userEmail} role updated to ${newRole.displayName}',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (error) {
      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Show error message with enhanced styling
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error,
                    color: Theme.of(context).colorScheme.onPrimary),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    'Failed to update role: $error',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            duration: Duration(seconds: 4),
          ),
        );
      }
    }
  }

  Future<void> _removeMember(BusinessMemberModel member) async {
    if (!mounted) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning_rounded, color: Colors.orange),
            SizedBox(width: 2.w),
            Text('Remove Member'),
          ],
        ),
        content: Text(
            'Are you sure you want to remove ${member.userFullName ?? member.userEmail} from this business? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.pop(context, true),
            style: FilledButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Remove'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => _buildLoadingDialog('Removing member...'),
      );

      try {
        await _memberService.removeMember(member.id, widget.businessId);

        // Close loading dialog
        if (mounted) Navigator.pop(context);

        await _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle,
                      color: Theme.of(context).colorScheme.onPrimary),
                  SizedBox(width: 2.w),
                  Expanded(
                    child: Text(
                      '${member.userFullName ?? member.userEmail} removed from business',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8)),
              duration: Duration(seconds: 3),
            ),
          );
        }
      } catch (error) {
        // Close loading dialog
        if (mounted) Navigator.pop(context);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.error,
                      color: Theme.of(context).colorScheme.onPrimary),
                  SizedBox(width: 2.w),
                  Expanded(
                    child: Text(
                      'Failed to remove member: $error',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8)),
              duration: Duration(seconds: 4),
            ),
          );
        }
      }
    }
  }

  Future<void> _cancelInvitation(BusinessInvitationModel invitation) async {
    if (!mounted) return;

    try {
      await _memberService.cancelInvitation(invitation.id, widget.businessId);
      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle,
                    color: Theme.of(context).colorScheme.onPrimary),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    'Invitation to ${invitation.email} cancelled',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.orange,
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error,
                    color: Theme.of(context).colorScheme.onPrimary),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    'Failed to cancel invitation: $error',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            duration: Duration(seconds: 4),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor:
          isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          SliverAppBar(
            expandedHeight: 50.0,
            floating: false,
            pinned: true,
            elevation: 0,
            backgroundColor: colorScheme.surface,
            foregroundColor: colorScheme.onSurface,
            flexibleSpace: FlexibleSpaceBar(
              background: SafeArea(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.groups_rounded,
                          color: colorScheme.primary,
                          size: 28,
                        ),
                        SizedBox(width: 4.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Team Members',
                                style: theme.textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: colorScheme.onSurface,
                                ),
                              ),
                              SizedBox(height: 0.0.h),
                              Text(
                                widget.businessName,
                                style: theme.textTheme.bodyLarge?.copyWith(
                                  color: colorScheme.onSurface
                                      .withValues(alpha: 0.7),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(48),
              child: Container(
                color: colorScheme.surface,
                child: TabBar(
                  controller: _tabController,
                  indicatorColor: colorScheme.primary,
                  indicatorWeight: 3,
                  labelColor: colorScheme.primary,
                  unselectedLabelColor:
                      colorScheme.onSurface.withValues(alpha: 0.6),
                  labelStyle: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  unselectedLabelStyle: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  tabs: [
                    Tab(
                      text: 'Members (${_members.length})',
                      icon: const Icon(Icons.people_rounded, size: 20),
                    ),
                    Tab(
                      text: 'Invitations (${_invitations.length})',
                      icon: const Icon(Icons.mail_outline_rounded, size: 20),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
        body: _isLoading
            ? _buildLoadingState()
            : _error != null
                ? _buildErrorState()
                : TabBarView(
                    controller: _tabController,
                    children: [
                      // Members Tab
                      _buildMembersTab(),
                      // Invitations Tab
                      _buildInvitationsTab(),
                    ],
                  ),
      ),
      floatingActionButton:
          _canManageMembers ? _buildFloatingActionButton(isDark) : null,
    );
  }

  Widget _buildLoadingState() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: CircularProgressIndicator(
              color: colorScheme.primary,
              strokeWidth: 3,
            ),
          ),
          SizedBox(height: 3.h),
          Text(
            'Loading team members...',
            style: theme.textTheme.titleMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(6.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                color: colorScheme.error.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.error_outline_rounded,
                size: 48,
                color: colorScheme.error,
              ),
            ),
            SizedBox(height: 3.h),
            Text(
              'Failed to load members',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              _error!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 4.h),
            FilledButton.icon(
              onPressed: _loadData,
              icon: const Icon(Icons.refresh_rounded),
              label: const Text('Try Again'),
              style: FilledButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 1.5.h),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton(bool isDark) {
    return FloatingActionButton.extended(
      onPressed: _showInviteMemberBottomSheet,
      icon: const Icon(Icons.person_add_rounded),
      label: const Text('Invite Member'),
      backgroundColor: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
      foregroundColor: Theme.of(context).colorScheme.onPrimary,
      elevation: 4,
      extendedPadding: EdgeInsets.symmetric(horizontal: 6.w),
    );
  }

  Widget _buildMembersTab() {
    if (_members.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            SizedBox(height: 2.h),
            Text(
              'No Members Yet',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            SizedBox(height: 1.h),
            Text(
              'Invite team members to collaborate on this business',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            if (_canManageMembers) ...[
              SizedBox(height: 3.h),
              ElevatedButton.icon(
                onPressed: _showInviteMemberBottomSheet,
                icon: const Icon(Icons.person_add),
                label: const Text('Invite First Member'),
              ),
            ],
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: EdgeInsets.all(4.w),
        itemCount: _members.length,
        itemBuilder: (context, index) {
          final member = _members[index];
          return FutureBuilder<bool>(
            future: _memberService.isBusinessCreator(
                widget.businessId, member.userId),
            builder: (context, snapshot) {
              final isBusinessCreator = snapshot.data ?? false;
              return MemberCardWidget(
                member: member,
                canManage: _canManageMembers,
                isBusinessCreator: isBusinessCreator,
                onUpdateRole: (newRole) => _updateMemberRole(member, newRole),
                onRemove: () => _removeMember(member),
                onManagePermissions: () {
                  // Navigate to permissions screen
                  Navigator.pushNamed(
                    context,
                    AppRoutes.memberPermissions,
                    arguments: {
                      'member': member,
                      'businessId': widget.businessId,
                    },
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildInvitationsTab() {
    if (_invitations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.mail_outline,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            SizedBox(height: 2.h),
            Text(
              'No Pending Invitations',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            SizedBox(height: 1.h),
            Text(
              'Send invitations to add team members',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: EdgeInsets.all(4.w),
        itemCount: _invitations.length,
        itemBuilder: (context, index) {
          final invitation = _invitations[index];
          return InvitationCardWidget(
            invitation: invitation,
            canManage: _canManageMembers,
            onCancel: () => _cancelInvitation(invitation),
            onResend: () {
              // Implement resend functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                    content: Text('Resend functionality coming soon')),
              );
            },
          );
        },
      ),
    );
  }

  // Enhanced loading dialog for better user feedback
  Widget _buildLoadingDialog(String message) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AlertDialog(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: CircularProgressIndicator(
              color: colorScheme.primary,
              strokeWidth: 3,
            ),
          ),
          SizedBox(height: 3.h),
          Text(
            message,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 1.h),
          Text(
            'Please wait...',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    );
  }
}
