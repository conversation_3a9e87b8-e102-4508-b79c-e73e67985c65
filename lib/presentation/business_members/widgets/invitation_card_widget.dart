import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class InvitationCardWidget extends StatefulWidget {
  final BusinessInvitationModel invitation;
  final bool canManage;
  final VoidCallback onCancel;
  final VoidCallback onResend;

  const InvitationCardWidget({
    super.key,
    required this.invitation,
    required this.canManage,
    required this.onCancel,
    required this.onResend,
  });

  @override
  State<InvitationCardWidget> createState() => _InvitationCardWidgetState();
}

class _InvitationCardWidgetState extends State<InvitationCardWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _expandController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _expandAnimation;
  late Animation<double> _rotationAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _expandController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _expandAnimation = CurvedAnimation(
      parent: _expandController,
      curve: Curves.easeInOut,
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 0.5).animate(
      CurvedAnimation(parent: _expandController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _expandController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _expandController.forward();
      } else {
        _expandController.reverse();
      }
    });
  }

  Color _getStatusColor() {
    switch (widget.invitation.status) {
      case InvitationStatus.pending:
        return Colors.orange;
      case InvitationStatus.accepted:
        return Colors.green;
      case InvitationStatus.declined:
        return Colors.red;
      case InvitationStatus.expired:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon() {
    switch (widget.invitation.status) {
      case InvitationStatus.pending:
        return Icons.schedule;
      case InvitationStatus.accepted:
        return Icons.check_circle;
      case InvitationStatus.declined:
        return Icons.cancel;
      case InvitationStatus.expired:
        return Icons.access_time;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks week${weeks > 1 ? 's' : ''} ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months month${months > 1 ? 's' : ''} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years year${years > 1 ? 's' : ''} ago';
    }
  }

  String _formatDateShort(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '${weeks}w ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '${months}m ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '${years}y ago';
    }
  }

  Widget _buildStatChip(String text, IconData icon, Color color) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 12),
          SizedBox(width: 1.w),
          Text(
            text,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleChip() {
    Color roleColor;
    IconData roleIcon;

    switch (widget.invitation.role) {
      case MemberRole.partner:
        roleColor = Colors.purple;
        roleIcon = Icons.diamond_rounded;
        break;
      case MemberRole.admin:
        roleColor = Colors.orange;
        roleIcon = Icons.admin_panel_settings_rounded;
        break;
      case MemberRole.staff:
        roleColor = Colors.blue;
        roleIcon = Icons.person_rounded;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: roleColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(roleIcon, color: roleColor, size: 14),
          SizedBox(width: 4),
          Text(
            widget.invitation.role.displayName,
            style: TextStyle(
              color: roleColor,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip() {
    final statusColor = _getStatusColor();
    final statusIcon = _getStatusIcon();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, color: statusColor, size: 14),
          SizedBox(width: 4),
          Text(
            widget.invitation.status.displayName,
            style: TextStyle(
              color: statusColor,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'resend':
        widget.onResend();
        break;
      case 'cancel':
        widget.onCancel();
        break;
    }
  }

  void _showActionMenu(BuildContext context) {
    final button = context.findRenderObject() as RenderBox?;
    showMenu<String>(
      context: context,
      position: RelativeRect.fromLTRB(
        button?.localToGlobal(Offset.zero).dx ?? 0,
        button?.localToGlobal(Offset.zero).dy ?? 0,
        button?.localToGlobal(Offset.zero).dx ?? 0,
        (button?.localToGlobal(Offset.zero).dy ?? 0) +
            (button?.size.height ?? 0),
      ),
      items: [
        PopupMenuItem(value: 'resend', child: Text('Resend Invitation')),
        PopupMenuItem(value: 'cancel', child: Text('Cancel Invitation')),
      ],
    ).then((value) {
      if (value != null) _handleMenuAction(value);
    });
  }

  bool _isExpiringSoon() {
    final now = DateTime.now();
    final expiresAt = widget.invitation.expiresAt;
    final difference = expiresAt.difference(now);
    return difference.inDays <= 2 && difference.inDays >= 0;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Card(
              margin: EdgeInsets.only(bottom: 3.h),
              elevation: isDark ? 1 : 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: BorderSide(
                  color: _getStatusColor().withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              clipBehavior: Clip.hardEdge,
              child: Column(
                children: [
                  // === COLLAPSED HEADER (always visible) ===
                  InkWell(
                    onTap: _toggleExpanded,
                    child: Padding(
                      padding: EdgeInsets.all(4.w),
                      child: Row(
                        children: [
                          // Status Icon with Circle Background
                          Container(
                            width: 14.w,
                            height: 14.w,
                            decoration: BoxDecoration(
                              color: _getStatusColor().withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(14.w),
                            ),
                            child: Icon(
                              _getStatusIcon(),
                              color: _getStatusColor(),
                              size: 7.w,
                            ),
                          ),
                          SizedBox(width: 4.w),

                          // Email & Role/Status
                          Expanded(
                            child: Column(
                              spacing: 4.sp,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        widget.invitation.email,
                                        style: theme.textTheme.titleMedium
                                            ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: colorScheme.onSurface,
                                          fontSize: 12.sp,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                if (!_isExpanded)
                                  Row(
                                    children: [
                                      _buildRoleChip(),
                                      _buildStatusChip(),
                                      if (_isExpiringSoon()) ...[
                                        SizedBox(width: 2.w),
                                        _buildStatChip(
                                          'Expires soon',
                                          Icons.warning_rounded,
                                          Colors.orange,
                                        ),
                                      ],
                                    ],
                                  ),
                              ],
                            ),
                          ),

                          // Expand icon
                          RotationTransition(
                            turns: _rotationAnimation,
                            child: Icon(
                              Icons.expand_more,
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),

                          // Actions (only if manageable & pending)
                          if (widget.canManage &&
                              widget.invitation.status ==
                                  InvitationStatus.pending)
                            SizedBox(
                              width: 40,
                              child: IconButton(
                                padding: EdgeInsets.zero,
                                onPressed: () => _showActionMenu(context),
                                icon: Icon(
                                  Icons.more_vert,
                                  color: colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),

                  // === EXPANDED CONTENT (animated) ===
                  SizeTransition(
                    axisAlignment: -1,
                    sizeFactor: _expandAnimation,
                    child: Column(
                      children: [
                        Divider(height: 1, thickness: 0.5),
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 4.w, vertical: 2.h),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Invitation Details Row
                              Container(
                                padding: EdgeInsets.all(3.w),
                                decoration: BoxDecoration(
                                  color: colorScheme.surface
                                      .withValues(alpha: 0.4),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    _buildEnhancedDetailItem(
                                      'Sent',
                                      _formatDate(widget.invitation.createdAt),
                                      Icons.send,
                                      Colors.blue,
                                    ),
                                    VerticalDivider(
                                      width: 1,
                                      thickness: 1,
                                      color: colorScheme.outline
                                          .withValues(alpha: 0.2),
                                    ),
                                    _buildEnhancedDetailItem(
                                      'Expires',
                                      _formatDate(widget.invitation.expiresAt),
                                      Icons.schedule,
                                      _isExpiringSoon()
                                          ? Colors.orange
                                          : Colors.grey,
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 2.h),

                              // Inviter Info (if available)
                              if (widget.invitation.inviterName != null)
                                Container(
                                  padding: EdgeInsets.all(3.w),
                                  decoration: BoxDecoration(
                                    color: colorScheme.primary
                                        .withValues(alpha: 0.05),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: colorScheme.primary
                                          .withValues(alpha: 0.2),
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.person_outline_rounded,
                                        color: colorScheme.primary,
                                        size: 20,
                                      ),
                                      SizedBox(width: 3.w),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Invited by',
                                              style: theme.textTheme.bodySmall
                                                  ?.copyWith(
                                                color: colorScheme.onSurface
                                                    .withValues(alpha: 0.6),
                                              ),
                                            ),
                                            Text(
                                              widget.invitation.inviterName!,
                                              style: theme.textTheme.bodyMedium
                                                  ?.copyWith(
                                                fontWeight: FontWeight.bold,
                                                color: colorScheme.primary,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                              // Expiration Warning (if expiring soon)
                              if (_isExpiringSoon()) ...[
                                SizedBox(height: 2.h),
                                Container(
                                  padding: EdgeInsets.all(3.w),
                                  decoration: BoxDecoration(
                                    color: Colors.orange.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color:
                                          Colors.orange.withValues(alpha: 0.3),
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.warning_rounded,
                                        color: Colors.orange,
                                        size: 20,
                                      ),
                                      SizedBox(width: 2.w),
                                      Expanded(
                                        child: Text(
                                          'This invitation expires soon!',
                                          style: theme.textTheme.bodySmall
                                              ?.copyWith(
                                            color: Colors.orange.shade700,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEnhancedDetailItem(
      String label, String value, IconData icon, Color iconColor) {
    final theme = Theme.of(context);
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      children: [
        Icon(
          icon,
          color: iconColor,
          size: 20,
        ),
        SizedBox(height: 0.5.h),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurface.withValues(alpha: 0.6),
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 0.3.h),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
