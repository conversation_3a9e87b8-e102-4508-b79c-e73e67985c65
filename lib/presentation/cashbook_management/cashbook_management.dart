import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/active_cashbook_indicator.dart';
import './widgets/cashbook_card_widget.dart';
import './widgets/cashbook_creation_modal.dart';
import './widgets/empty_state_widget.dart';

class CashbookManagement extends StatefulWidget {
  const CashbookManagement({super.key});

  @override
  State<CashbookManagement> createState() => _CashbookManagementState();
}

class _CashbookManagementState extends State<CashbookManagement> {
  final TextEditingController _searchController = TextEditingController();

  // Data
  List<CashbookModel> _cashbooks = [];
  List<CashbookModel> _filteredCashbooks = [];
  List<BusinessModel> _businesses = [];
  List<BusinessInvitationModel> _receivedInvitations = [];
  CashbookModel? _activeCashbook;
  BusinessModel? _selectedBusiness;
  List<Map> _permissions = [];
  List<Map> _members = [];

  // State
  final Set<String> _selectedCashbooks = {};
  bool _isMultiSelectMode = false;
  String _searchQuery = '';
  int _currentBottomNavIndex = 0;
  bool _isLoading = true;
  String? _error;

  // Services
  late DataService _dataService;
  late AuthService _authService;
  late BusinessMemberService _memberService;

  @override
  void initState() {
    super.initState();
    _dataService = DataService.instance;
    _authService = AuthService.instance;
    _memberService = BusinessMemberService.instance;
    _searchController.addListener(_onSearchChanged);
    _loadData();
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    if (!_authService.isAuthenticated) {
      Navigator.pushReplacementNamed(context, '/login');
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load businesses, cashbooks, and invitations
      final futures = await Future.wait([
        _dataService.getUserBusinesses(),
        _memberService.getReceivedInvitations(),
      ]);

      _businesses = futures[0] as List<BusinessModel>;
      _receivedInvitations = futures[1] as List<BusinessInvitationModel>;

      // set active business from preferences
      String? activeBusinessId = PreferencesService.getActiveBusiness();

      if (activeBusinessId != null) {
        _selectedBusiness = _businesses.firstWhere(
          (b) => b.id == activeBusinessId,
          orElse: () => _businesses.first,
        );
      }

      // Set default business
      if (_businesses.isNotEmpty && _selectedBusiness == null) {
        setState(() {
          _selectedBusiness = _businesses.firstWhere(
            (b) => b.isDefault,
            orElse: () => _businesses.first,
          );
        });
      }

      if (_selectedBusiness == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      final cashbooksFutures = await Future.wait([
        _dataService.getUserCashbooks(_selectedBusiness!.id),
        _dataService.getDefaultCashbook(),
      ]);

      var cashbooks = cashbooksFutures[0] as List<CashbookModel>;
      _activeCashbook = cashbooksFutures[1] as CashbookModel?;

      _cashbooks = (_selectedBusiness != null)
          ? cashbooks
              .where(
                (cashbook) => cashbook.businessId == _selectedBusiness?.id,
              )
              .toList()
          : cashbooks;

      List<Map<String, dynamic>> permissions = [];
      List<Map<String, dynamic>> members = [];
      for (var c in _cashbooks) {
        var perms = await _dataService.getUserCashbookPermissions(c.id);
        permissions.add({'cashbook_id': c.id, 'permissions': perms});

        var m = await _dataService.countCashbookMembers(c);
        members.add({'cashbook_id': c.id, 'members': m});
      }

      _permissions = permissions;
      _members = members;

      print(_permissions);
      print(_members);

      _filteredCashbooks = List.from(_cashbooks);

      setState(() {
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _error = error.toString();
        _isLoading = false;
      });
      rethrow;
    }
  }

  Future<void> _respondToInvitation(
      BusinessInvitationModel invitation, bool accept) async {
    try {
      var confirmAction =
          'Are you sure you want to ${accept ? 'accept' : 'decline'} this invitation?';

      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Invitation Response'),
          content: Text(confirmAction),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: Text(accept ? 'Accept' : 'Decline'),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      await _memberService.respondToInvitation(invitation.id, accept);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(accept ? 'Invitation accepted' : 'Invitation declined'),
            backgroundColor: accept ? Colors.green : Colors.orange,
          ),
        );
        _loadData(); // Refresh data
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to respond to invitation: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
      _filteredCashbooks = _cashbooks.where((cashbook) {
        return cashbook.name.toLowerCase().contains(_searchQuery) ||
            (cashbook.description ?? '').toLowerCase().contains(_searchQuery);
      }).toList();
    });
  }

  void _showCreateCashbookModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CashbookCreationModal(
        onCreateCashbook: _createCashbook,
      ),
    );
  }

  Future<void> _createCashbook(Map<String, dynamic> newCashbookData) async {
    try {
      final cashbook = await _dataService.createCashbook(
        name: newCashbookData['name'],
        businessId: _selectedBusiness?.id,
        description: newCashbookData['description'],
        currency: newCashbookData['currency'] ?? 'USD',
        color: newCashbookData['color'],
        icon: newCashbookData['icon'],
        isDefault: newCashbookData['isDefault'] ?? false,
      );

      await _dataService.addCashbookMeta(cashbook);

      await _loadData(); // Refresh data

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Cashbook "${cashbook.name}" created'),
          backgroundColor: AppTheme.getSuccessColor(
              Theme.of(context).brightness == Brightness.dark),
        ),
      );
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to create cashbook: $error'),
          backgroundColor: AppTheme.getErrorColor(
              Theme.of(context).brightness == Brightness.dark),
        ),
      );
      rethrow;
    }
  }

  Future<void> _switchActiveCashbook(CashbookModel cashbook) async {
    try {
      // await _dataService.setDefaultCashbook(cashbook.id);

      setState(() {
        _activeCashbook = cashbook;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Switched to "${cashbook.name}"'),
          backgroundColor: AppTheme.getSuccessColor(
              Theme.of(context).brightness == Brightness.dark),
        ),
      );
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to switch cashbook: $error'),
          backgroundColor: AppTheme.getErrorColor(
              Theme.of(context).brightness == Brightness.dark),
        ),
      );
    }
  }

  Future<void> _deleteCashbook(CashbookModel cashbook) async {
    if (cashbook.isDefault) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Cannot delete default cashbook'),
          backgroundColor: AppTheme.getErrorColor(
              Theme.of(context).brightness == Brightness.dark),
        ),
      );
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Cashbook'),
        content: Text(
            'Are you sure you want to delete "${cashbook.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _dataService.deleteCashbook(cashbook.id);
        await _loadData(); // Refresh data

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Cashbook "${cashbook.name}" deleted'),
            backgroundColor: AppTheme.getErrorColor(
                Theme.of(context).brightness == Brightness.dark),
          ),
        );
      } catch (error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete cashbook: $error'),
            backgroundColor: AppTheme.getErrorColor(
                Theme.of(context).brightness == Brightness.dark),
          ),
        );
      }
    }
  }

  void _openCashbookDetails(CashbookModel cashbook) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildCashbookDetailsModal(cashbook),
    );
  }

  void _toggleMultiSelectMode() {
    setState(() {
      _isMultiSelectMode = !_isMultiSelectMode;
      if (!_isMultiSelectMode) {
        _selectedCashbooks.clear();
      }
    });
  }

  void _onBottomNavTap(int index) {
    setState(() {
      _currentBottomNavIndex = index;
    });

    switch (index) {
      case 0:
        Navigator.pop(context);
        Navigator.pushNamed(context, '/cashbook-management');
        break;
      case 1:
        Navigator.pushNamed(context, '/help');
        break;
      case 2:
        Navigator.pushNamed(context, '/settings', arguments: {
          'business_id': _selectedBusiness?.id,
        });
        break;
    }
  }

  void _toggleCashbookSelection(String cashbookId) {
    setState(() {
      if (_selectedCashbooks.contains(cashbookId)) {
        _selectedCashbooks.remove(cashbookId);
      } else {
        _selectedCashbooks.add(cashbookId);
      }
    });
  }

  Future<void> _performBulkAction(String action) async {
    switch (action) {
      case 'export':
        // TODO: Implement export functionality
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('coming soon...'),
            backgroundColor: AppTheme.getSuccessColor(
                Theme.of(context).brightness == Brightness.dark),
          ),
        );
        break;
      case 'archive':
        // TODO: Implement archive functionality
        try {
          bool? accept = await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: Text('Archive Cashbooks'),
              content: Text(
                  'Are you sure you want to archive ${_selectedCashbooks.length} selected cashbooks?'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: const Text('Archive'),
                ),
              ],
            ),
          );

          if (accept == false || accept == null) return;

          for (final cashbookId in _selectedCashbooks) {
            await _dataService.updateCashbook(
              cashbookId: cashbookId,
              isActive: false,
            );
          }

          await _loadData(); // Refresh data

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Archived ${_selectedCashbooks.length} cashbooks'),
              backgroundColor: AppTheme.getWarningColor(
                  Theme.of(context).brightness == Brightness.dark),
            ),
          );
        } catch (error) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to archive cashbooks: $error'),
              backgroundColor: AppTheme.getErrorColor(
                  Theme.of(context).brightness == Brightness.dark),
            ),
          );
        }
        break;
    }

    setState(() {
      _isMultiSelectMode = false;
      _selectedCashbooks.clear();
    });
  }

  void _onSelectBusiness() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildSelectBusinessModal(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Show loading state
    if (_isLoading) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: const Text('Cashbook Management'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Show error state
    if (_error != null) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: const Text('Cashbook Management'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              SizedBox(height: 2.h),
              Text(
                'Failed to load cashbooks',
                style: theme.textTheme.headlineSmall,
              ),
              SizedBox(height: 1.h),
              Text(
                _error!,
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 3.h),
              ElevatedButton(
                onPressed: _loadData,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: GestureDetector(
          onTap: _onSelectBusiness,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              IconButton(
                  onPressed: _onSelectBusiness,
                  icon: CustomIconWidget(
                    iconName: 'business',
                    color: theme.colorScheme.onSurface,
                    size: 6.w,
                  )),
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _selectedBusiness?.name ?? 'Personal',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    'Tap to switch business',
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              IconButton(
                  onPressed: _onSelectBusiness,
                  icon: CustomIconWidget(
                    iconName: 'arrow_drop_down',
                    color: theme.colorScheme.onSurface,
                    size: 4.w,
                  )),
            ],
          ),
        ),
        actions: [
          if (_isMultiSelectMode) ...[
            IconButton(
              onPressed: () => _performBulkAction('export'),
              icon: CustomIconWidget(
                iconName: 'download',
                color: theme.colorScheme.onSurface,
                size: 6.w,
              ),
              tooltip: 'Export Selected',
            ),
            IconButton(
              onPressed: () => _performBulkAction('archive'),
              icon: CustomIconWidget(
                iconName: 'archive',
                color: theme.colorScheme.onSurface,
                size: 6.w,
              ),
              tooltip: 'Archive Selected',
            ),
            IconButton(
              onPressed: _toggleMultiSelectMode,
              icon: CustomIconWidget(
                iconName: 'close',
                color: theme.colorScheme.onSurface,
                size: 6.w,
              ),
              tooltip: 'Cancel Selection',
            ),
          ] else ...[
            if (_selectedBusiness != null)
              IconButton(
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/business-members',
                    arguments: {
                      'businessId': _selectedBusiness!.id,
                      'businessName': _selectedBusiness!.name,
                    },
                  );
                },
                icon: CustomIconWidget(
                  iconName: 'people',
                  color: theme.colorScheme.onSurface,
                  size: 6.w,
                ),
                tooltip: 'Manage Team',
              ),
            IconButton(
              onPressed: _cashbooks.isNotEmpty ? _toggleMultiSelectMode : null,
              icon: CustomIconWidget(
                iconName: 'checklist',
                color: theme.colorScheme.onSurface,
                size: 6.w,
              ),
              tooltip: 'Multi-select',
            ),
          ],
        ],
      ),
      body: SafeArea(
        child: _cashbooks.isEmpty && _receivedInvitations.isEmpty
            ? EmptyStateWidget(onCreateFirst: _showCreateCashbookModal)
            : Column(
                children: [
                  if (_activeCashbook != null)
                    ActiveCashbookIndicator(
                      activeCashbook: _convertCashbookToMap(_activeCashbook!),
                    ),
                  if (_receivedInvitations.isNotEmpty)
                    _buildInvitationsSection(theme),
                  _buildSearchBar(theme),
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: _loadData,
                      child: _buildCashbooksList(),
                    ),
                  ),
                ],
              ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentBottomNavIndex,
        onTap: _onBottomNavTap,
        type: BottomNavigationBarType.fixed,
        backgroundColor: theme.colorScheme.surface,
        selectedItemColor: theme.colorScheme.primary,
        unselectedItemColor: theme.colorScheme.onSurface.withValues(alpha: 0.6),
        elevation: 8,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            activeIcon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.help_outline),
            activeIcon: Icon(Icons.help),
            label: 'Help',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings_outlined),
            activeIcon: Icon(Icons.settings),
            label: 'Settings',
          )
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showCreateCashbookModal,
        icon: CustomIconWidget(
          iconName: 'add',
          color: Theme.of(context).colorScheme.onPrimary,
          size: 5.w,
        ),
        label: const Text('New Cashbook'),
      ),
    );
  }

  // Convert CashbookModel to Map for widget compatibility
  Map<String, dynamic> _convertCashbookToMap(CashbookModel cashbook) {
    return {
      "id": cashbook.id,
      "name": cashbook.name,
      "description": cashbook.description ?? '',
      "currency": cashbook.currency,
      "color": cashbook.color ?? "0xFF1B365D",
      "icon": cashbook.icon ?? "account_balance_wallet",
      "balance": cashbook.balance,
      "transactionCount": cashbook.transactionCount,
      "lastActivity": cashbook.lastActivity,
      "isDefault": cashbook.isDefault,
      "template": "General", // TODO: Load template name
      "createdAt": cashbook.createdAt,
      'permissions':
          _permissions.firstWhere((p) => p['cashbook_id'] == cashbook.id),
      'members':
          _members.firstWhere((p) => p['cashbook_id'] == cashbook.id)['members']
    };
  }

  Widget _buildSearchBar(ThemeData theme) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search cashbooks...',
          prefixIcon: Padding(
            padding: EdgeInsets.all(3.w),
            child: CustomIconWidget(
              iconName: 'search',
              color: theme.colorScheme.onSurfaceVariant,
              size: 5.w,
            ),
          ),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                  },
                  icon: CustomIconWidget(
                    iconName: 'clear',
                    color: theme.colorScheme.onSurfaceVariant,
                    size: 5.w,
                  ),
                )
              : null,
        ),
      ),
    );
  }

  Widget _buildCashbooksList() {
    if (_filteredCashbooks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: 'search_off',
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              size: 15.w,
            ),
            SizedBox(height: 2.h),
            Text(
              'No cashbooks found',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 1.h),
            Text(
              'Try adjusting your search terms',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _filteredCashbooks.length,
      itemBuilder: (context, index) {
        final cashbook = _filteredCashbooks[index];
        final isActive = _activeCashbook?.id == cashbook.id;
        final isSelected = _selectedCashbooks.contains(cashbook.id);

        return CashbookCardWidget(
          cashbook: _convertCashbookToMap(cashbook),
          isActive: isActive,
          isSelected: _isMultiSelectMode ? isSelected : false,
          onTap: () {
            if (_isMultiSelectMode) {
              _toggleCashbookSelection(cashbook.id);
            } else {
              _openCashbookDetails(cashbook);
            }
          },
          onLongPress: () {
            if (!_isMultiSelectMode) {
              _toggleMultiSelectMode();
              _toggleCashbookSelection(cashbook.id);
            }
          },
          onSwipeRight: () => _switchActiveCashbook(cashbook),
          onSwipeLeft: () => _deleteCashbook(cashbook),
        );
      },
    );
  }

  Widget _buildCashbookDetailsModal(CashbookModel cashbook) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      height: 70.h,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: theme.dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Cashbook Details',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: CustomIconWidget(
                    iconName: 'close',
                    color: theme.colorScheme.onSurface,
                    size: 6.w,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(4.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 15.w,
                        height: 7.h,
                        decoration: BoxDecoration(
                          color: cashbook.color != null
                              ? Color(
                                  int.tryParse(cashbook.color!) ?? 0xFF1B365D)
                              : const Color(0xFF1B365D),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Center(
                          child: CustomIconWidget(
                            iconName: cashbook.icon ?? "account_balance_wallet",
                            color: Theme.of(context).colorScheme.onPrimary,
                            size: 8.w,
                          ),
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              cashbook.name,
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            SizedBox(height: 0.5.h),
                            Text(
                              cashbook.description ?? 'No description',
                              style: theme.textTheme.bodyMedium,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 3.h),
                  Container(
                    padding: EdgeInsets.all(4.w),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Current Balance',
                                  style: theme.textTheme.labelMedium,
                                ),
                                SizedBox(height: 0.5.h),
                                Text(
                                  '${cashbook.currency} ${cashbook.balance.toStringAsFixed(2)}',
                                  style:
                                      theme.textTheme.headlineMedium?.copyWith(
                                    fontWeight: FontWeight.w700,
                                    color: cashbook.balance >= 0
                                        ? AppTheme.getSuccessColor(isDark)
                                        : AppTheme.getErrorColor(isDark),
                                  ),
                                ),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  'Transactions',
                                  style: theme.textTheme.labelMedium,
                                ),
                                SizedBox(height: 0.5.h),
                                Text(
                                  '${cashbook.transactionCount}',
                                  style:
                                      theme.textTheme.headlineMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 3.h),
                  _buildDetailRow(theme, 'Currency', cashbook.currency),
                  _buildDetailRow(
                      theme, 'Created', _formatDate(cashbook.createdAt)),
                  _buildDetailRow(theme, 'Last Activity',
                      _formatDate(cashbook.lastActivity)),
                  SizedBox(height: 4.h),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            Navigator.pushNamed(
                                context, '/transaction-history');
                          },
                          icon: CustomIconWidget(
                            iconName: 'history',
                            color: theme.colorScheme.primary,
                            size: 5.w,
                          ),
                          label: const Text('View History'),
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            Navigator.pushNamed(context, '/dashboard',
                                arguments: {'cashbook_id': cashbook.id});
                          },
                          icon: CustomIconWidget(
                            iconName: 'dashboard',
                            color: Theme.of(context).colorScheme.onPrimary,
                            size: 5.w,
                          ),
                          label: const Text('Dashboard'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(ThemeData theme, String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 1.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.month}/${date.day}/${date.year}';
    }
  }

  Widget _buildSelectBusinessModal() {
    return Container(
      height: 50.h,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Select Business',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: CustomIconWidget(
                    iconName: 'close',
                    color: Theme.of(context).colorScheme.onSurface,
                    size: 6.w,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: _businesses.length,
              itemBuilder: (context, index) {
                final business = _businesses[index];
                return ListTile(
                  leading: const Icon(Icons.business),
                  title: Row(
                    spacing: 2.w,
                    children: [
                      Text(business.name),
                      if (business.role != null)
                        Badge(
                          padding: EdgeInsets.symmetric(horizontal: 2.w),
                          textColor: Theme.of(context).colorScheme.onSurface,
                          backgroundColor: Theme.of(context)
                              .colorScheme
                              .primary
                              .withValues(alpha: 0.1),
                          label: Text(
                            business.role!,
                          ),
                        ),
                    ],
                  ),
                  subtitle: Text(business.description ?? 'No description'),
                  trailing: _selectedBusiness?.id == business.id
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () async {
                    setState(() {
                      _selectedBusiness = business;
                    });
                    await PreferencesService.setActiveBusiness(
                        _selectedBusiness!.id);
                    Navigator.pop(context);
                    _loadData(); // Refresh cashbooks for selected business
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvitationsSection(ThemeData theme) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(2.w),
                ),
                child: Icon(
                  Icons.mail_outline,
                  color: theme.colorScheme.primary,
                  size: 5.w,
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Business Invitations',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      '${_receivedInvitations.length} pending invitation${_receivedInvitations.length == 1 ? '' : 's'}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color:
                            theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          SizedBox(
            height: 20.h,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _receivedInvitations.length,
              itemBuilder: (context, index) {
                final invitation = _receivedInvitations[index];
                return _buildInvitationCard(theme, invitation);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvitationCard(
      ThemeData theme, BusinessInvitationModel invitation) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.92,
      margin: EdgeInsets.only(right: 3.w),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(3.w),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(2.w),
                ),
                child: Icon(
                  Icons.business,
                  color: theme.colorScheme.primary,
                  size: 4.w,
                ),
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      invitation.businessName ?? 'Unknown Business',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      'by ${invitation.inviterName ?? 'Unknown'}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color:
                            theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          Container(
            padding: EdgeInsets.all(2.w),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(2.w),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.badge_outlined,
                  size: 3.w,
                  color: theme.colorScheme.primary,
                ),
                SizedBox(width: 1.w),
                Text(
                  invitation.role.displayName,
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          if (invitation.status == InvitationStatus.pending) ...[
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _respondToInvitation(invitation, false),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: theme.colorScheme.error,
                      side: BorderSide(color: theme.colorScheme.error),
                      padding: EdgeInsets.symmetric(vertical: 1.h),
                    ),
                    child: const Text('Decline'),
                  ),
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _respondToInvitation(invitation, true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 1.h),
                    ),
                    child: const Text('Accept'),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
