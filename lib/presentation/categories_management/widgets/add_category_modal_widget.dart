import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

/// Modal widget for creating new categories with icon picker and color selector
class AddCategoryModalWidget extends StatefulWidget {
  final String categoryType;
  final Function(Map<String, dynamic>) onCategoryAdded;
  final List<Map<String, dynamic>> existingCategories;

  const AddCategoryModalWidget({
    super.key,
    required this.categoryType,
    required this.onCategoryAdded,
    required this.existingCategories,
  });

  @override
  State<AddCategoryModalWidget> createState() => _AddCategoryModalWidgetState();
}

class _AddCategoryModalWidgetState extends State<AddCategoryModalWidget> {
  final TextEditingController _nameController = TextEditingController();
  String _selectedIcon = 'category';
  Color _selectedColor = const Color(0xFF1B365D);
  String _errorMessage = '';

  // Available icons for categories
  final List<String> _availableIcons = [
    'category',
    'shopping_cart',
    'restaurant',
    'local_gas_station',
    'home',
    'directions_car',
    'school',
    'local_hospital',
    'fitness_center',
    'movie',
    'shopping_bag',
    'coffee',
    'fastfood',
    'local_grocery_store',
    'phone',
    'wifi',
    'electrical_services',
    'water_drop',
    'local_laundry_service',
    'pets',
    'child_care',
    'elderly',
    'work',
    'business',
    'account_balance',
    'savings',
    'credit_card',
    'payment',
    'monetization_on',
    'trending_up',
    'pie_chart',
    'bar_chart',
    'analytics',
    'receipt',
    'local_atm',
  ];

  // Available colors for categories
  final List<Color> _availableColors = [
    const Color(0xFF1B365D),
    const Color(0xFF2E7D32),
    const Color(0xFFC62828),
    const Color(0xFFF57C00),
    const Color(0xFF1976D2),
    const Color(0xFF7B1FA2),
    const Color(0xFF388E3C),
    const Color(0xFFD32F2F),
    const Color(0xFFF57F17),
    const Color(0xFF1565C0),
    const Color(0xFF512DA8),
    const Color(0xFF00796B),
    const Color(0xFF5D4037),
    const Color(0xFF455A64),
    const Color(0xFFE91E63),
    const Color(0xFF9C27B0),
    const Color(0xFF673AB7),
    const Color(0xFF3F51B5),
  ];

  @override
  void initState() {
    super.initState();
    _selectedColor = widget.categoryType == 'income'
        ? const Color(0xFF2E7D32)
        : const Color(0xFFC62828);
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      constraints: BoxConstraints(
        maxHeight: 85.h,
        minHeight: 60.h,
      ),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: EdgeInsets.only(top: 2.h),
            width: 12.w,
            height: 0.5.h,
            decoration: BoxDecoration(
              color: isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.all(4.w),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: CustomIconWidget(
                    iconName: 'close',
                    color: isDark
                        ? const Color(0xFFB0B0B0)
                        : const Color(0xFF757575),
                    size: 6.w,
                  ),
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: Text(
                    'Add ${widget.categoryType.toUpperCase()} Category',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: _validateAndSave,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.categoryType == 'income'
                        ? AppTheme.getSuccessColor(isDark)
                        : AppTheme.getErrorColor(isDark),
                    foregroundColor: Colors.white,
                    padding:
                        EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                  ),
                  child: Text(
                    'Save',
                    style: theme.textTheme.labelLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Category Name Input
                  _buildSectionTitle('Category Name'),
                  SizedBox(height: 1.h),
                  TextField(
                    controller: _nameController,
                    decoration: InputDecoration(
                      hintText: 'Enter category name',
                      errorText:
                          _errorMessage.isNotEmpty ? _errorMessage : null,
                      prefixIcon: Padding(
                        padding: EdgeInsets.all(3.w),
                        child: CustomIconWidget(
                          iconName: _selectedIcon,
                          color: _selectedColor,
                          size: 5.w,
                        ),
                      ),
                    ),
                    onChanged: (value) {
                      if (_errorMessage.isNotEmpty) {
                        setState(() {
                          _errorMessage = '';
                        });
                      }
                    },
                  ),

                  SizedBox(height: 3.h),

                  // Icon Picker
                  _buildSectionTitle('Choose Icon'),
                  SizedBox(height: 1.h),
                  _buildIconPicker(),

                  SizedBox(height: 3.h),

                  // Color Selector
                  _buildSectionTitle('Choose Color'),
                  SizedBox(height: 1.h),
                  _buildColorSelector(),

                  SizedBox(height: 4.h),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    final theme = Theme.of(context);
    return Text(
      title,
      style: theme.textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildIconPicker() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      constraints: BoxConstraints(
        maxHeight: 25.h,
      ),
      decoration: BoxDecoration(
        border: Border.all(
          color: isDark ? const Color(0xFF424242) : const Color(0xFFE0E0E0),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: GridView.builder(
        padding: EdgeInsets.all(2.w),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 6,
          crossAxisSpacing: 2.w,
          mainAxisSpacing: 1.h,
          childAspectRatio: 1,
        ),
        itemCount: _availableIcons.length,
        itemBuilder: (context, index) {
          final icon = _availableIcons[index];
          final isSelected = icon == _selectedIcon;

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedIcon = icon;
              });
            },
            child: Container(
              decoration: BoxDecoration(
                color: isSelected
                    ? _selectedColor.withValues(alpha: 0.1)
                    : Colors.transparent,
                border: Border.all(
                  color: isSelected ? _selectedColor : Colors.transparent,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: CustomIconWidget(
                  iconName: icon,
                  color: isSelected
                      ? _selectedColor
                      : (isDark
                          ? const Color(0xFFB0B0B0)
                          : const Color(0xFF757575)),
                  size: 6.w,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildColorSelector() {
    return Wrap(
      spacing: 2.w,
      runSpacing: 1.h,
      children: _availableColors.map((color) {
        final isSelected = color == _selectedColor;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedColor = color;
            });
          },
          child: Container(
            width: 12.w,
            height: 6.h,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
              border: isSelected
                  ? Border.all(
                      color: Theme.of(context).colorScheme.onPrimary,
                      width: 3,
                    )
                  : null,
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: color.withValues(alpha: 0.4),
                        offset: const Offset(0, 2),
                        blurRadius: 8,
                        spreadRadius: 0,
                      ),
                    ]
                  : null,
            ),
            child: isSelected
                ? Center(
                    child: CustomIconWidget(
                      iconName: 'check',
                      color: Theme.of(context).colorScheme.onPrimary,
                      size: 4.w,
                    ),
                  )
                : null,
          ),
        );
      }).toList(),
    );
  }

  void _validateAndSave() {
    final name = _nameController.text.trim();

    if (name.isEmpty) {
      setState(() {
        _errorMessage = 'Category name is required';
      });
      return;
    }

    if (name.length < 2) {
      setState(() {
        _errorMessage = 'Category name must be at least 2 characters';
      });
      return;
    }

    // Check for duplicate names
    final isDuplicate = widget.existingCategories.any(
      (category) =>
          (category['name'] as String).toLowerCase() == name.toLowerCase() &&
          (category['type'] as String) == widget.categoryType,
    );

    if (isDuplicate) {
      setState(() {
        _errorMessage = 'A category with this name already exists';
      });
      return;
    }

    // Create new category
    final newCategory = {
      'id': DateTime.now().millisecondsSinceEpoch,
      'name': name,
      'icon': _selectedIcon,
      'color': _selectedColor.value,
      'type': widget.categoryType,
      'isDefault': false,
      'transactionCount': 0,
      'createdAt': DateTime.now(),
    };

    widget.onCategoryAdded(newCategory);
    Navigator.of(context).pop();
  }
}
