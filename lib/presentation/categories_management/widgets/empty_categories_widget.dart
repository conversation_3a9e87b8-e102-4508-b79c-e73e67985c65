import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

/// Widget displayed when no categories are found or created
class EmptyCategoriesWidget extends StatelessWidget {
  final String categoryType;
  final VoidCallback onAddCategory;
  final bool isSearchResult;
  final String searchQuery;

  const EmptyCategoriesWidget({
    super.key,
    required this.categoryType,
    required this.onAddCategory,
    this.isSearchResult = false,
    this.searchQuery = '',
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.all(6.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Illustration
          Container(
            width: 30.w,
            height: 15.h,
            decoration: BoxDecoration(
              color: (categoryType == 'income'
                      ? AppTheme.getSuccessColor(isDark)
                      : AppTheme.getErrorColor(isDark))
                  .withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Center(
              child: CustomIconWidget(
                iconName: isSearchResult ? 'search_off' : 'category',
                color: categoryType == 'income'
                    ? AppTheme.getSuccessColor(isDark)
                    : AppTheme.getErrorColor(isDark),
                size: 12.w,
              ),
            ),
          ),

          SizedBox(height: 3.h),

          // Title
          Text(
            isSearchResult
                ? 'No Results Found'
                : 'No ${categoryType.toUpperCase()} Categories',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: isDark ? Colors.white : const Color(0xFF212121),
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 1.h),

          // Description
          Text(
            isSearchResult
                ? 'No categories match "$searchQuery". Try a different search term or create a new category.'
                : 'Create your first ${categoryType} category to start organizing your transactions better.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isDark ? const Color(0xFFB0B0B0) : const Color(0xFF757575),
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 4.h),

          // Action Button
          ElevatedButton.icon(
            onPressed: onAddCategory,
            style: ElevatedButton.styleFrom(
              backgroundColor: categoryType == 'income'
                  ? AppTheme.getSuccessColor(isDark)
                  : AppTheme.getErrorColor(isDark),
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            icon: CustomIconWidget(
              iconName: 'add',
              color: Theme.of(context).colorScheme.onPrimary,
              size: 5.w,
            ),
            label: Text(
              isSearchResult
                  ? 'Create Category'
                  : 'Add ${categoryType.toUpperCase()} Category',
              style: theme.textTheme.labelLarge?.copyWith(
                color: Theme.of(context).colorScheme.onPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          if (!isSearchResult) ...[
            SizedBox(height: 3.h),

            // Suggested Categories
            Text(
              'Suggested Categories:',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : const Color(0xFF212121),
              ),
            ),

            SizedBox(height: 2.h),

            Wrap(
              spacing: 2.w,
              runSpacing: 1.h,
              children: _getSuggestedCategories().map((suggestion) {
                return Container(
                  padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                  decoration: BoxDecoration(
                    color: (categoryType == 'income'
                            ? AppTheme.getSuccessColor(isDark)
                            : AppTheme.getErrorColor(isDark))
                        .withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: categoryType == 'income'
                          ? AppTheme.getSuccessColor(isDark)
                          : AppTheme.getErrorColor(isDark),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CustomIconWidget(
                        iconName: suggestion['icon'] as String,
                        color: categoryType == 'income'
                            ? AppTheme.getSuccessColor(isDark)
                            : AppTheme.getErrorColor(isDark),
                        size: 4.w,
                      ),
                      SizedBox(width: 1.w),
                      Text(
                        suggestion['name'] as String,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: categoryType == 'income'
                              ? AppTheme.getSuccessColor(isDark)
                              : AppTheme.getErrorColor(isDark),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  List<Map<String, String>> _getSuggestedCategories() {
    if (categoryType == 'income') {
      return [
        {'name': 'Salary', 'icon': 'work'},
        {'name': 'Freelance', 'icon': 'business'},
        {'name': 'Investment', 'icon': 'trending_up'},
        {'name': 'Bonus', 'icon': 'monetization_on'},
      ];
    } else {
      return [
        {'name': 'Food', 'icon': 'restaurant'},
        {'name': 'Transport', 'icon': 'directions_car'},
        {'name': 'Shopping', 'icon': 'shopping_cart'},
        {'name': 'Bills', 'icon': 'receipt'},
      ];
    }
  }
}
