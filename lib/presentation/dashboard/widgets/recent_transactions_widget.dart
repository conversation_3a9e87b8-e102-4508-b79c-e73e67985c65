import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class RecentTransactionsWidget extends StatelessWidget {
  final String cashbookId;
  final List<Map<String, dynamic>> transactions;
  final List<dynamic> permissions;
  final VoidCallback onRefresh;
  final Function(TransactionModel) onEditTransaction;
  final Function(TransactionModel) onDeleteTransaction;
  final String? currency;

  const RecentTransactionsWidget({
    super.key,
    required this.currency,
    required this.permissions,
    required this.cashbookId,
    required this.transactions,
    required this.onRefresh,
    required this.onEditTransaction,
    required this.onDeleteTransaction,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: 90.w,
      margin: EdgeInsets.symmetric(horizontal: 5.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Transactions',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.pushNamed(context, '/transaction-history',
                      arguments: {
                        'cashbook_id': cashbookId,
                      });
                },
                child: Text(
                  'View All',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          // SizedBox(height: 2.h),
          transactions.isEmpty
              ? _buildEmptyState(context, permissions)
              : RefreshIndicator(
                  onRefresh: () async {
                    onRefresh();
                  },
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount:
                        transactions.length > 10 ? 10 : transactions.length,
                    separatorBuilder: (context, index) => SizedBox(height: 1.h),
                    itemBuilder: (context, index) {
                      final transaction = transactions[index];
                      return _buildTransactionItem(
                          context, transaction, currency!);
                    },
                  ),
                ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, List permissions) {
    final theme = Theme.of(context);

    print(permissions);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(8.w),
      margin: EdgeInsets.only(top: 2.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          CustomIconWidget(
            iconName: 'receipt_long',
            color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
            size: 48,
          ),
          SizedBox(height: 2.h),
          Text(
            'No Transactions Yet',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            permissions.contains('write')
                ? 'Add your first transaction to get started'
                : '',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 3.h),
          permissions.contains('write')
              ? ElevatedButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/add-transaction',
                        arguments: {'cashbookId': this.cashbookId});
                  },
                  child: const Text('Add Transaction'))
              : Text('You are not permitted to add transactions'),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(
      BuildContext context, Map<String, dynamic> transaction, String currency) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final isIncome = (transaction['type'] as String) == 'income';
    final amount = transaction['amount'] as double;
    final category = transaction['category'] as String;
    final description = transaction['description'] as String;
    final date = transaction['date'] as DateTime;
    final categoryIcon = transaction['categoryIcon'] as String;
    final fullName = transaction['fullName'] ?? '';

    return Dismissible(
      key: Key(transaction['id'] as String),
      confirmDismiss: (dir) async {
        if (permissions.contains('write')) {
          return await showDialog<bool>(
            context: context,
            builder: (context) {
              return AlertDialog(
                title: Text(
                    "${dir == DismissDirection.startToEnd ? 'Edit Transaction' : 'Delete Transaction'}"),
                content: Text(
                  dir == DismissDirection.startToEnd
                      ? 'Are you sure you want to edit this transaction?'
                      : 'Are you sure you want to delete this transaction?',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    child: Text(
                        dir == DismissDirection.startToEnd ? 'Edit' : 'Delete'),
                  ),
                ],
              );
            },
          );
        } else {
          return await showDialog<bool>(
            context: context,
            builder: (context) {
              return AlertDialog(
                title: Text('Not Allowed'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    child: Text('OK'),
                  ),
                ],
              );
            },
          );
        }
      },
      background: Container(
        decoration: BoxDecoration(
          color: AppTheme.getSuccessColor(isDark),
          borderRadius: BorderRadius.circular(12),
        ),
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.only(left: 4.w),
        child: CustomIconWidget(
          iconName: 'edit',
          color: Theme.of(context).colorScheme.onPrimary,
          size: 24,
        ),
      ),
      secondaryBackground: Container(
        decoration: BoxDecoration(
          color: AppTheme.getErrorColor(isDark),
          borderRadius: BorderRadius.circular(12),
        ),
        alignment: Alignment.centerRight,
        padding: EdgeInsets.only(right: 4.w),
        child: CustomIconWidget(
          iconName: 'delete',
          color: Theme.of(context).colorScheme.onPrimary,
          size: 24,
        ),
      ),
      onDismissed: (direction) {
        if (direction == DismissDirection.startToEnd) {
          onEditTransaction(TransactionModel.fromJson(transaction));
        } else {
          onDeleteTransaction(TransactionModel.fromJson(transaction));
        }
      },
      child: Container(
        padding: EdgeInsets.all(3.w),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 12.w,
              height: 12.w,
              decoration: BoxDecoration(
                color: isIncome
                    ? AppTheme.getSuccessColor(isDark).withValues(alpha: 0.1)
                    : AppTheme.getErrorColor(isDark).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: CustomIconWidget(
                  iconName: categoryIcon,
                  color: isIncome
                      ? AppTheme.getSuccessColor(isDark)
                      : AppTheme.getErrorColor(isDark),
                  size: 20,
                ),
              ),
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    category,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 0.5.h),
                  Text(
                    description,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 0.5.h),
                  Text(
                    '${date.day}/${date.month}/${date.year}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                      fontSize: 10.sp,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${isIncome ? '+' : '-'} ${currency} ${amount.toStringAsFixed(2)}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isIncome
                        ? AppTheme.getSuccessColor(isDark)
                        : AppTheme.getErrorColor(isDark),
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  'Created by: $fullName',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                    fontSize: 8.sp,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
