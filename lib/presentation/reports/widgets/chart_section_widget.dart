import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class ChartSectionWidget extends StatefulWidget {
  final int selectedChartType;
  final Function(int) onChartTypeChanged;
  final List<Map<String, dynamic>> chartData;

  const ChartSectionWidget({
    super.key,
    required this.selectedChartType,
    required this.onChartTypeChanged,
    required this.chartData,
  });

  @override
  State<ChartSectionWidget> createState() => _ChartSectionWidgetState();
}

class _ChartSectionWidgetState extends State<ChartSectionWidget> {
  int touchedIndex = -1;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.white.withValues(alpha: 0.05)
                : Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Financial Overview',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              _buildChartTypeSelector(theme, isDark),
            ],
          ),
          SizedBox(height: 3.h),
          SizedBox(
            height: 30.h,
            child: _buildChart(theme, isDark),
          ),
          if (widget.selectedChartType == 1) ...[
            SizedBox(height: 2.h),
            _buildPieChartLegend(theme, isDark),
          ],
        ],
      ),
    );
  }

  Widget _buildChartTypeSelector(ThemeData theme, bool isDark) {
    final List<IconData> chartIcons = [
      Icons.bar_chart,
      Icons.pie_chart,
      Icons.show_chart,
    ];

    return Row(
      children: List.generate(chartIcons.length, (index) {
        final isSelected = index == widget.selectedChartType;

        return GestureDetector(
          onTap: () => widget.onChartTypeChanged(index),
          child: Container(
            margin: EdgeInsets.only(left: index > 0 ? 2.w : 0),
            padding: EdgeInsets.all(2.w),
            decoration: BoxDecoration(
              color: isSelected
                  ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                      .withValues(alpha: 0.1)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected
                    ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                    : Colors.transparent,
                width: 1,
              ),
            ),
            child: CustomIconWidget(
              iconName: chartIcons[index].codePoint.toString(),
              color: isSelected
                  ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                  : (isDark
                      ? AppTheme.textSecondaryDark
                      : AppTheme.textSecondaryLight),
              size: 20,
            ),
          ),
        );
      }),
    );
  }

  Widget _buildChart(ThemeData theme, bool isDark) {
    switch (widget.selectedChartType) {
      case 0:
        return _buildBarChart(theme, isDark);
      case 1:
        return _buildPieChart(theme, isDark);
      case 2:
        return _buildLineChart(theme, isDark);
      default:
        return _buildBarChart(theme, isDark);
    }
  }

  Widget _buildBarChart(ThemeData theme, bool isDark) {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: 10000,
        barTouchData: BarTouchData(
          touchTooltipData: BarTouchTooltipData(
            tooltipBgColor:
                isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              return BarTooltipItem(
                '\${rod.toY.round()}',
                theme.textTheme.bodySmall!,
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
                return Text(
                  months[value.toInt() % months.length],
                  style: theme.textTheme.bodySmall,
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  '\$${(value / 1000).toStringAsFixed(0)}k',
                  style: theme.textTheme.bodySmall,
                );
              },
            ),
          ),
          topTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        barGroups: List.generate(6, (index) {
          final incomeData = widget.chartData.isNotEmpty
              ? (widget.chartData[index % widget.chartData.length]['income']
                  as double)
              : (5000 + (index * 500)).toDouble();
          final expenseData = widget.chartData.isNotEmpty
              ? (widget.chartData[index % widget.chartData.length]['expense']
                  as double)
              : (3000 + (index * 300)).toDouble();

          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: incomeData,
                color: AppTheme.getSuccessColor(isDark),
                width: 3.w,
                borderRadius: BorderRadius.circular(4),
              ),
              BarChartRodData(
                toY: expenseData,
                color: AppTheme.getErrorColor(isDark),
                width: 3.w,
                borderRadius: BorderRadius.circular(4),
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildPieChart(ThemeData theme, bool isDark) {
    return PieChart(
      PieChartData(
        pieTouchData: PieTouchData(
          touchCallback: (FlTouchEvent event, pieTouchResponse) {
            setState(() {
              if (!event.isInterestedForInteractions ||
                  pieTouchResponse == null ||
                  pieTouchResponse.touchedSection == null) {
                touchedIndex = -1;
                return;
              }
              touchedIndex =
                  pieTouchResponse.touchedSection!.touchedSectionIndex;
            });
          },
        ),
        borderData: FlBorderData(show: false),
        sectionsSpace: 2,
        centerSpaceRadius: 15.w,
        sections: _buildPieChartSections(isDark),
      ),
    );
  }

  List<PieChartSectionData> _buildPieChartSections(bool isDark) {
    final List<Map<String, dynamic>> categories = [
      {'name': 'Food', 'value': 35.0, 'color': const Color(0xFF4CAF50)},
      {'name': 'Transport', 'value': 25.0, 'color': const Color(0xFF2196F3)},
      {'name': 'Shopping', 'value': 20.0, 'color': const Color(0xFFFF9800)},
      {'name': 'Bills', 'value': 15.0, 'color': const Color(0xFFF44336)},
      {'name': 'Others', 'value': 5.0, 'color': const Color(0xFF9C27B0)},
    ];

    return List.generate(categories.length, (index) {
      final isTouched = index == touchedIndex;
      final fontSize = isTouched ? 14.sp : 12.sp;
      final radius = isTouched ? 25.w : 22.w;

      return PieChartSectionData(
        color: categories[index]['color'] as Color,
        value: categories[index]['value'] as double,
        title: '${categories[index]['value']}%',
        radius: radius,
        titleStyle: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.onPrimary,
        ),
      );
    });
  }

  Widget _buildPieChartLegend(ThemeData theme, bool isDark) {
    final List<Map<String, dynamic>> categories = [
      {'name': 'Food', 'value': 35.0, 'color': const Color(0xFF4CAF50)},
      {'name': 'Transport', 'value': 25.0, 'color': const Color(0xFF2196F3)},
      {'name': 'Shopping', 'value': 20.0, 'color': const Color(0xFFFF9800)},
      {'name': 'Bills', 'value': 15.0, 'color': const Color(0xFFF44336)},
      {'name': 'Others', 'value': 5.0, 'color': const Color(0xFF9C27B0)},
    ];

    return Wrap(
      spacing: 4.w,
      runSpacing: 1.h,
      children: categories.map((category) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 3.w,
              height: 3.w,
              decoration: BoxDecoration(
                color: category['color'] as Color,
                shape: BoxShape.circle,
              ),
            ),
            SizedBox(width: 2.w),
            Text(
              category['name'] as String,
              style: theme.textTheme.bodySmall,
            ),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildLineChart(ThemeData theme, bool isDark) {
    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: 2000,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
                return Text(
                  months[value.toInt() % months.length],
                  style: theme.textTheme.bodySmall,
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  '\$${(value / 1000).toStringAsFixed(0)}k',
                  style: theme.textTheme.bodySmall,
                );
              },
            ),
          ),
          topTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        minX: 0,
        maxX: 5,
        minY: 0,
        maxY: 10000,
        lineBarsData: [
          LineChartBarData(
            spots: List.generate(6, (index) {
              final value = widget.chartData.isNotEmpty
                  ? (widget.chartData[index % widget.chartData.length]['income']
                      as double)
                  : (5000 + (index * 500)).toDouble();
              return FlSpot(index.toDouble(), value);
            }),
            isCurved: true,
            color: AppTheme.getSuccessColor(isDark),
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: const FlDotData(show: true),
            belowBarData: BarAreaData(
              show: true,
              color: AppTheme.getSuccessColor(isDark).withValues(alpha: 0.1),
            ),
          ),
          LineChartBarData(
            spots: List.generate(6, (index) {
              final value = widget.chartData.isNotEmpty
                  ? (widget.chartData[index % widget.chartData.length]
                      ['expense'] as double)
                  : (3000 + (index * 300)).toDouble();
              return FlSpot(index.toDouble(), value);
            }),
            isCurved: true,
            color: AppTheme.getErrorColor(isDark),
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: const FlDotData(show: true),
            belowBarData: BarAreaData(
              show: true,
              color: AppTheme.getErrorColor(isDark).withValues(alpha: 0.1),
            ),
          ),
        ],
      ),
    );
  }
}
