import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  // Data
  UserModel? _currentUser;
  BusinessModel? _activeBusiness;
  List<BusinessModel> _businesses = [];
  bool _isLoading = true;
  String? _error;

  // Services
  late DataService _dataService;
  late AuthService _authService;

  @override
  void initState() {
    super.initState();
    _dataService = DataService.instance;
    _authService = AuthService.instance;
    _loadUserData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _handleNavigationArguments();
  }

  void _handleNavigationArguments() async {
    setState(() {
      _isLoading = true;
    });
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>;
    String? businessId = args['business_id'] as String?;

    _activeBusiness = await _dataService.getBusinessById(businessId!);

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _loadUserData() async {
    if (!_authService.isAuthenticated) {
      Navigator.pushReplacementNamed(context, '/login');
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load user profile and businesses
      final futures = await Future.wait([
        _authService.getCurrentUserProfile(),
        _dataService.getUserBusinesses(),
        _dataService.getDefaultBusiness(),
      ]);

      _currentUser = futures[0] as UserModel?;
      _businesses = futures[1] as List<BusinessModel>;
      _activeBusiness = futures[2] as BusinessModel?;

      setState(() {
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _error = error.toString();
        _isLoading = false;
      });
    }
  }

  int get _profilePercentage {
    if (_currentUser == null) return 0;

    int completedFields = 0;
    int totalFields = 4;

    if (_currentUser!.fullName != null && _currentUser!.fullName!.isNotEmpty)
      completedFields++;
    if (_currentUser!.phone != null && _currentUser!.phone!.isNotEmpty)
      completedFields++;
    if (_currentUser!.avatarUrl != null && _currentUser!.avatarUrl!.isNotEmpty)
      completedFields++;
    if (_businesses.isNotEmpty) completedFields++;

    return ((completedFields / totalFields) * 100).round();
  }

  String get _profileStrength {
    final percentage = _profilePercentage;
    if (percentage >= 80) return 'Strong';
    if (percentage >= 50) return 'Medium';
    return 'Weak';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Show loading state
    if (_isLoading) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: const Text('Settings'),
          backgroundColor: theme.appBarTheme.backgroundColor,
          elevation: 0,
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Show error state
    if (_error != null) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: const Text('Settings'),
          backgroundColor: theme.appBarTheme.backgroundColor,
          elevation: 0,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              SizedBox(height: 2.h),
              Text(
                'Failed to load settings',
                style: theme.textTheme.headlineSmall,
              ),
              SizedBox(height: 1.h),
              Text(
                _error!,
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 3.h),
              ElevatedButton(
                onPressed: _loadUserData,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: theme.appBarTheme.backgroundColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _showSignOutDialog,
            tooltip: 'Sign Out',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile completion section
            _buildProfileSection(theme),
            SizedBox(height: 3.h),

            // Business settings section
            if (_businesses.isNotEmpty) ...[
              _buildBusinessSection(theme),
              SizedBox(height: 3.h),
            ],

            // General settings section
            Text(
              'General Settings',
              style: theme.textTheme.titleSmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 2.h),

            _buildAppSettingsItem(theme),
            _buildYourProfileItem(theme),
            _buildAboutCashBookItem(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileSection(ThemeData theme) {
    final userName = _currentUser?.fullName ?? 'User';
    final isProfileIncomplete = _profilePercentage < 80;

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 12.w,
                height: 12.w,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: _currentUser?.avatarUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          _currentUser!.avatarUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Icon(
                            Icons.person_outline,
                            color: theme.colorScheme.primary,
                            size: 6.w,
                          ),
                        ),
                      )
                    : Icon(
                        Icons.person_outline,
                        color: theme.colorScheme.primary,
                        size: 6.w,
                      ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      userName,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (isProfileIncomplete)
                      Row(
                        children: [
                          Icon(
                            Icons.warning_amber_rounded,
                            color: Colors.orange,
                            size: 4.w,
                          ),
                          SizedBox(width: 1.w),
                          Text(
                            'Incomplete profile',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.orange,
                            ),
                          ),
                        ],
                      )
                    else
                      Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: Colors.green,
                            size: 4.w,
                          ),
                          SizedBox(width: 1.w),
                          Text(
                            'Profile complete',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
              IconButton(
                onPressed: _onEditProfile,
                icon: Icon(
                  Icons.edit_outlined,
                  color: theme.colorScheme.primary,
                  size: 5.w,
                ),
              ),
            ],
          ),
          SizedBox(height: 3.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Profile Strength: $_profileStrength',
                style: theme.textTheme.bodyMedium,
              ),
              Text(
                '$_profilePercentage%',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 1.h),
          LinearProgressIndicator(
            value: _profilePercentage / 100,
            backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(
              _profilePercentage >= 80
                  ? Colors.green
                  : _profilePercentage >= 50
                      ? Colors.orange
                      : Colors.red,
            ),
          ),
          if (isProfileIncomplete) ...[
            SizedBox(height: 2.h),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _onAddMissingDetails,
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: EdgeInsets.symmetric(vertical: 2.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'COMPLETE PROFILE',
                      style: theme.textTheme.labelLarge?.copyWith(
                        color: theme.colorScheme.onPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(width: 2.w),
                    Icon(
                      Icons.arrow_forward,
                      size: 4.w,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBusinessSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Business Settings',
          style: theme.textTheme.titleSmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 2.h),
        _buildRenameBusinessItem(theme),
        _buildManageBusinessMembersItem(theme),
        _buildDeleteBusinessItem(theme),
      ],
    );
  }

  Widget _buildRenameBusinessItem(ThemeData theme) {
    return _buildSettingsItem(
      theme: theme,
      icon: Icons.edit_outlined,
      title: 'Rename Business',
      subtitle: 'Change your business name',
      onTap: _onRenameBusiness,
    );
  }

  Widget _buildManageBusinessMembersItem(ThemeData theme) {
    return _buildSettingsItem(
      theme: theme,
      icon: Icons.people_outlined,
      title: 'Manage Business Members',
      subtitle: 'Add, remove, and manage team members',
      onTap: _onManageBusinessMembers,
    );
  }

  Widget _buildDeleteBusinessItem(ThemeData theme) {
    return _buildSettingsItem(
      theme: theme,
      icon: Icons.delete_outline,
      title: 'Delete Business',
      subtitle: 'Permanently delete this business',
      onTap: _onDeleteBusiness,
      showDivider: false,
    );
  }

  Widget _buildAppSettingsItem(ThemeData theme) {
    return _buildSettingsItem(
      theme: theme,
      icon: Icons.settings_outlined,
      title: 'App Settings',
      subtitle: 'Language, Theme, Security, Backup',
      onTap: () => Navigator.pushNamed(context, '/app-settings'),
    );
  }

  Widget _buildYourProfileItem(ThemeData theme) {
    return _buildSettingsItem(
      theme: theme,
      icon: Icons.person_outline,
      title: 'Your Profile',
      subtitle: 'Name, Mobile Number, Email',
      onTap: _onEditProfile,
    );
  }

  Widget _buildAboutCashBookItem(ThemeData theme) {
    return _buildSettingsItem(
      theme: theme,
      icon: Icons.info_outline,
      title: 'About CashBook',
      subtitle: 'Privacy policy, T&C, About us',
      onTap: () => Navigator.pushNamed(context, '/about'),
      showDivider: false,
    );
  }

  Widget _buildSettingsItem({
    required ThemeData theme,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool showDivider = true,
  }) {
    return Column(
      children: [
        ListTile(
          leading: Container(
            width: 10.w,
            height: 10.w,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: theme.colorScheme.primary,
              size: 5.w,
            ),
          ),
          title: Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          subtitle: Text(
            subtitle,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          trailing: Icon(
            Icons.arrow_forward_ios,
            size: 4.w,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
          ),
          onTap: onTap,
          contentPadding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
        ),
        if (showDivider)
          Divider(
            height: 1,
            color: theme.dividerColor.withValues(alpha: 0.1),
          ),
      ],
    );
  }

  void _onEditProfile() {
    showDialog(
      context: context,
      builder: (context) => _buildEditProfileDialog(),
    );
  }

  Widget _buildEditProfileDialog() {
    final theme = Theme.of(context);
    final nameController =
        TextEditingController(text: _currentUser?.fullName ?? '');
    final phoneController =
        TextEditingController(text: _currentUser?.phone ?? '');

    return AlertDialog(
      title: const Text('Edit Profile'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: nameController,
            decoration: const InputDecoration(
              labelText: 'Full Name',
              border: OutlineInputBorder(),
            ),
          ),
          SizedBox(height: 2.h),
          TextField(
            controller: phoneController,
            decoration: const InputDecoration(
              labelText: 'Phone Number',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.phone,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () =>
              _saveProfile(nameController.text, phoneController.text),
          child: const Text('Save'),
        ),
      ],
    );
  }

  Future<void> _saveProfile(String fullName, String phone) async {
    try {
      await _authService.updateProfile(
        fullName: fullName.isNotEmpty ? fullName : null,
        phone: phone.isNotEmpty ? phone : null,
      );

      await _loadUserData(); // Refresh data
      Navigator.pop(context);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Profile updated successfully')),
      );
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to update profile: $error')),
      );
    }
  }

  void _onAddMissingDetails() {
    _onEditProfile();
  }

  void _showSignOutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: _signOut,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }

  Future<void> _signOut() async {
    try {
      await _authService.signOut();
      Navigator.pushNamedAndRemoveUntil(
        context,
        '/login',
        (route) => false,
      );
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to sign out: $error')),
      );
    }
  }

  Widget _buildRenameBusinessDialog() {
    final theme = Theme.of(context);
    final nameController =
        TextEditingController(text: _activeBusiness?.name ?? '');

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Enter a new name for your business',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        SizedBox(height: 2.h),
        TextField(
          controller: nameController,
          decoration: InputDecoration(
            labelText: 'Business Name',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            prefixIcon: const Icon(Icons.business),
          ),
          textCapitalization: TextCapitalization.words,
          autofocus: true,
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              FilledButton(
                onPressed: () => _saveBusinessName(nameController.text.trim()),
                child: const Text('Save'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDeleteBusinessDialog() {
    final theme = Theme.of(context);

    return AlertDialog(
      title: Row(
        children: [
          Container(
            width: 8.w,
            height: 8.w,
            decoration: BoxDecoration(
              color: theme.colorScheme.error.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.warning_rounded,
              color: theme.colorScheme.error,
              size: 4.w,
            ),
          ),
          SizedBox(width: 3.w),
          const Text('Delete Business'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Are you sure you want to delete "${_activeBusiness?.name}"?',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 2.h),
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: theme.colorScheme.error.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.error.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'This action cannot be undone. All associated data will be permanently deleted:',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.error,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 1.h),
                Text('• Cashbooks and transactions',
                    style: theme.textTheme.bodySmall),
                Text('• Team members and invitations',
                    style: theme.textTheme.bodySmall),
                Text('• Business settings and data',
                    style: theme.textTheme.bodySmall),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: _deleteActiveBusiness,
          style: FilledButton.styleFrom(
            backgroundColor: theme.colorScheme.error,
            foregroundColor: theme.colorScheme.onError,
          ),
          child: const Text('Delete'),
        ),
      ],
    );
  }

  void _onRenameBusiness() {
    if (_activeBusiness == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No active business found')),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        height: 60.h,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20.0),
            topRight: Radius.circular(20.0),
          ),
        ),
        padding: EdgeInsets.all(4.w),
        child: _buildRenameBusinessDialog(),
      ),
    );
  }

  void _onManageBusinessMembers() {
    if (_activeBusiness == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No active business found')),
      );
      return;
    }

    Navigator.pushNamed(
      context,
      '/business-members',
      arguments: {
        'businessId': _activeBusiness!.id,
        'businessName': _activeBusiness!.name,
      },
    );
  }

  void _onDeleteBusiness() {
    if (_activeBusiness == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No active business found')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => _buildDeleteBusinessDialog(),
    );
  }

  Future<void> _saveBusinessName(String newName) async {
    if (newName.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Business name cannot be empty')),
      );
      return;
    }

    if (newName == _activeBusiness?.name) {
      Navigator.pop(context);
      return;
    }

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      await _dataService.updateBusiness(
        businessId: _activeBusiness!.id,
        name: newName,
      );

      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Close rename dialog
      if (mounted) Navigator.pop(context);

      // Refresh data
      await _loadUserData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle,
                    color: Theme.of(context).colorScheme.onPrimary),
                SizedBox(width: 2.w),
                const Expanded(
                  child: Text(
                    'Business name updated successfully',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (error) {
      // Close loading dialog
      if (mounted) Navigator.pop(context);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error,
                    color: Theme.of(context).colorScheme.onPrimary),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    'Failed to update business name: $error',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  Future<void> _deleteActiveBusiness() async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      await _dataService.deleteBusiness(_activeBusiness!.id);

      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Close delete dialog
      if (mounted) Navigator.pop(context);

      // Refresh data
      await _loadUserData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle,
                    color: Theme.of(context).colorScheme.onPrimary),
                SizedBox(width: 2.w),
                const Expanded(
                  child: Text(
                    'Business deleted successfully',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (error) {
      // Close loading dialog
      if (mounted) Navigator.pop(context);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error,
                    color: Theme.of(context).colorScheme.onPrimary),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    'Failed to delete business: $error',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }
}
