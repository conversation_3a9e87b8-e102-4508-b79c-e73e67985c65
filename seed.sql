SET session_replication_role = replica;

--
-- PostgreSQL database dump
--

-- \restrict znqSJW5lvpyKq3afXi8GURLTlgLS2xyOcnm6ndjUuFt1lDFEXUa4rwLM1OGRXn0

-- Dumped from database version 17.6
-- Dumped by pg_dump version 17.6

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: audit_log_entries; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."audit_log_entries" ("instance_id", "id", "payload", "created_at", "ip_address") VALUES
	('********-0000-0000-0000-************', 'cbe5fef5-b34b-4c69-9311-8158e30ff2dc', '{"action":"user_signedup","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"provider":"email","user_email":"<EMAIL>","user_id":"9f394cc1-352a-4937-af01-271b201614dd","user_phone":""}}', '2025-09-02 07:38:39.799909+00', ''),
	('********-0000-0000-0000-************', '479368ee-f200-4e5e-b2ca-c756d93a95d0', '{"action":"login","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}', '2025-09-02 07:38:42.786821+00', ''),
	('********-0000-0000-0000-************', '0735f45c-0918-4b55-8738-1d5e4c94c7d2', '{"action":"login","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}', '2025-09-02 07:39:40.666589+00', ''),
	('********-0000-0000-0000-************', 'd603d7a3-217d-4187-a2b1-925cbd027ca5', '{"action":"login","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}', '2025-09-02 07:40:00.034359+00', ''),
	('********-0000-0000-0000-************', '3873eada-569f-4e9d-9db6-438fd61e4ebc', '{"action":"token_refreshed","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-02 08:39:31.790902+00', ''),
	('********-0000-0000-0000-************', '9e0f2660-f017-462e-b1a4-039a0a669de9', '{"action":"token_revoked","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-02 08:39:31.791237+00', ''),
	('********-0000-0000-0000-************', '83a43a3e-f3e2-4fd1-aa03-18d52aed0321', '{"action":"token_refreshed","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-02 09:39:03.203277+00', ''),
	('********-0000-0000-0000-************', '5cc69db9-0bbb-422f-9f8d-6281850af920', '{"action":"token_revoked","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-02 09:39:03.203633+00', ''),
	('********-0000-0000-0000-************', 'c435e3ff-cc2d-4f83-9e01-6f8a9a9d61e3', '{"action":"token_refreshed","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-02 10:38:26.732613+00', ''),
	('********-0000-0000-0000-************', '506d46d4-b807-4a77-bcd4-f7d752f7a794', '{"action":"token_revoked","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-02 10:38:26.732962+00', ''),
	('********-0000-0000-0000-************', 'edb09245-aa28-48a4-8102-46c6185eae3b', '{"action":"token_refreshed","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-02 11:58:52.337355+00', ''),
	('********-0000-0000-0000-************', '71b436f7-c1f1-4fdd-81a1-299b7a75dfee', '{"action":"token_revoked","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-02 11:58:52.339319+00', ''),
	('********-0000-0000-0000-************', 'd06b3684-c3a3-4b84-a1c1-84b7602734e8', '{"action":"token_refreshed","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-03 18:21:34.388697+00', ''),
	('********-0000-0000-0000-************', '16940b30-0181-4cb2-858c-6c149d94286f', '{"action":"token_revoked","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-03 18:21:34.390552+00', ''),
	('********-0000-0000-0000-************', '9c9eb804-e245-4e1e-9bd7-59df17a2429b', '{"action":"token_refreshed","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-03 19:21:03.715546+00', ''),
	('********-0000-0000-0000-************', '9952448a-cb00-4e05-bbbe-10750e1ecc4a', '{"action":"token_revoked","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-03 19:21:03.716091+00', ''),
	('********-0000-0000-0000-************', 'd85420d6-9fe4-46df-b799-1c04d1347bb9', '{"action":"token_refreshed","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-04 14:37:13.822774+00', ''),
	('********-0000-0000-0000-************', '2687bb2a-f68d-49b6-bfd8-776e54d19489', '{"action":"token_revoked","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-04 14:37:13.824785+00', ''),
	('********-0000-0000-0000-************', '0aa67eba-1569-4d99-89e9-abf3745ea9c1', '{"action":"token_refreshed","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-05 09:27:31.475224+00', ''),
	('********-0000-0000-0000-************', '9826e582-2749-4c7d-b0b1-e84179ca5f7a', '{"action":"token_revoked","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-05 09:27:31.477956+00', ''),
	('********-0000-0000-0000-************', '3b2fabe5-09f4-4a6f-9768-148d2c018076', '{"action":"token_refreshed","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-05 10:26:55.004549+00', ''),
	('********-0000-0000-0000-************', '520f5293-b8df-48cc-9ab3-cf9caa17be00', '{"action":"token_revoked","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-05 10:26:55.005714+00', ''),
	('********-0000-0000-0000-************', '5a0bc067-e5c5-4ca3-91c0-512b13fdc63b', '{"action":"token_refreshed","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-05 11:26:17.617606+00', ''),
	('********-0000-0000-0000-************', '65ab8c98-13e4-4390-bbcb-e4a7bf28fdfa', '{"action":"token_revoked","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-05 11:26:17.618143+00', ''),
	('********-0000-0000-0000-************', '602af041-b6e1-4db2-8adf-8170a2600bd3', '{"action":"token_refreshed","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-12 20:15:42.640503+00', ''),
	('********-0000-0000-0000-************', 'c9536219-3b15-4560-96a3-db7959dedf83', '{"action":"token_revoked","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-12 20:15:42.643614+00', ''),
	('********-0000-0000-0000-************', 'e7d887bd-1754-4630-9e4f-b5f4262471ce', '{"action":"login","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}', '2025-09-29 20:19:06.31416+00', ''),
	('********-0000-0000-0000-************', '539625a1-92e0-4c3e-a113-94685b6ce86f', '{"action":"token_refreshed","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-29 21:18:36.195477+00', ''),
	('********-0000-0000-0000-************', '0900a35a-d06d-4f62-b819-41d2d18cf089', '{"action":"token_revoked","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-29 21:18:36.195856+00', ''),
	('********-0000-0000-0000-************', 'd2d1ddf6-2768-4005-919f-2c90187bdba6', '{"action":"token_refreshed","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-29 22:18:18.812985+00', ''),
	('********-0000-0000-0000-************', '503cd942-8b10-4fd6-9272-fb52140bd543', '{"action":"token_revoked","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-29 22:18:18.813278+00', ''),
	('********-0000-0000-0000-************', '123e4d4a-668c-4857-8dd3-c5c4e4d29913', '{"action":"logout","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}', '2025-09-29 22:51:22.34665+00', ''),
	('********-0000-0000-0000-************', '98a0665f-d336-47b7-aba8-44c96ba2e36f', '{"action":"user_signedup","actor_id":"2f0f0a4d-be1c-413f-859c-dbc2c4c0bc3d","actor_name":"BRIANMSYAMBOZA","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team","traits":{"provider":"email"}}', '2025-09-29 22:51:33.462196+00', ''),
	('********-0000-0000-0000-************', '200909a4-01a4-41f0-8a35-fd681e0ae093', '{"action":"login","actor_id":"2f0f0a4d-be1c-413f-859c-dbc2c4c0bc3d","actor_name":"BRIANMSYAMBOZA","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}', '2025-09-29 22:51:33.464638+00', ''),
	('********-0000-0000-0000-************', 'ebd30590-79c3-4e27-b951-88f1b89c135c', '{"action":"user_repeated_signup","actor_id":"2f0f0a4d-be1c-413f-859c-dbc2c4c0bc3d","actor_name":"BRIANMSYAMBOZA","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}', '2025-09-29 22:51:38.732018+00', ''),
	('********-0000-0000-0000-************', '12362f91-d3e1-4fea-9cbd-ebc12deef5d1', '{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"2f0f0a4d-be1c-413f-859c-dbc2c4c0bc3d","user_phone":""}}', '2025-09-29 22:53:10.678546+00', ''),
	('********-0000-0000-0000-************', '35d0d60c-aef9-4432-aadc-e3567c74c89f', '{"action":"user_signedup","actor_id":"2569a47b-3c0b-4f64-8241-e8ee5d8f28bf","actor_name":"BRIANMSYAMBOZA","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team","traits":{"provider":"email"}}', '2025-09-29 22:55:16.693588+00', ''),
	('********-0000-0000-0000-************', '0cff8dff-c084-4a9a-b723-efea3544669c', '{"action":"login","actor_id":"2569a47b-3c0b-4f64-8241-e8ee5d8f28bf","actor_name":"BRIANMSYAMBOZA","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}', '2025-09-29 22:55:16.695498+00', ''),
	('********-0000-0000-0000-************', 'fbfaaa6e-5f4b-427f-97da-600ef52b7a6b', '{"action":"user_repeated_signup","actor_id":"2569a47b-3c0b-4f64-8241-e8ee5d8f28bf","actor_name":"BRIANMSYAMBOZA","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}', '2025-09-29 22:55:40.593117+00', ''),
	('********-0000-0000-0000-************', '75506e95-2f50-4be9-aafd-d6cc3229f12d', '{"action":"user_deleted","actor_id":"********-0000-0000-0000-************","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"user_email":"<EMAIL>","user_id":"2569a47b-3c0b-4f64-8241-e8ee5d8f28bf","user_phone":""}}', '2025-09-29 22:55:55.553559+00', ''),
	('********-0000-0000-0000-************', '1e52e3ab-621a-4790-b196-ecc157f40bde', '{"action":"user_signedup","actor_id":"7a5402a0-3ff1-460c-ae39-9a0e40974558","actor_name":"BRIANMSYAMBOZA","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team","traits":{"provider":"email"}}', '2025-09-29 22:55:59.3923+00', ''),
	('********-0000-0000-0000-************', 'b2baf9dd-c558-48fe-879b-bb458bc45bf5', '{"action":"login","actor_id":"7a5402a0-3ff1-460c-ae39-9a0e40974558","actor_name":"BRIANMSYAMBOZA","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}', '2025-09-29 22:55:59.394147+00', ''),
	('********-0000-0000-0000-************', '9f56843a-9dfa-4306-8cbe-f9760b88e5f4', '{"action":"logout","actor_id":"7a5402a0-3ff1-460c-ae39-9a0e40974558","actor_name":"BRIANMSYAMBOZA","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}', '2025-09-29 22:58:28.532538+00', ''),
	('********-0000-0000-0000-************', '90a71cac-84a5-45a8-a248-e6ef08b1444c', '{"action":"login","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}', '2025-09-29 22:58:32.802533+00', ''),
	('********-0000-0000-0000-************', 'a0f49a24-e1e2-4beb-9f6e-ce667d00bb42', '{"action":"token_refreshed","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-30 14:48:04.967014+00', ''),
	('********-0000-0000-0000-************', '52aeacd4-79be-4c4c-a9e0-817015fccdf7', '{"action":"token_revoked","actor_id":"9f394cc1-352a-4937-af01-271b201614dd","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-09-30 14:48:04.968126+00', '');


--
-- Data for Name: flow_state; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: users; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."users" ("instance_id", "id", "aud", "role", "email", "encrypted_password", "email_confirmed_at", "invited_at", "confirmation_token", "confirmation_sent_at", "recovery_token", "recovery_sent_at", "email_change_token_new", "email_change", "email_change_sent_at", "last_sign_in_at", "raw_app_meta_data", "raw_user_meta_data", "is_super_admin", "created_at", "updated_at", "phone", "phone_confirmed_at", "phone_change", "phone_change_token", "phone_change_sent_at", "email_change_token_current", "email_change_confirm_status", "banned_until", "reauthentication_token", "reauthentication_sent_at", "is_sso_user", "deleted_at", "is_anonymous") VALUES
	('********-0000-0000-0000-************', '7a5402a0-3ff1-460c-ae39-9a0e40974558', 'authenticated', 'authenticated', '<EMAIL>', '$2a$10$lYo58YfI4zItwE3LWYS7RuBJni64qHne/AA2FZ.g2HmR24nPdmJuq', '2025-09-29 22:55:59.39264+00', NULL, '', NULL, '', NULL, '', '', NULL, '2025-09-29 22:55:59.394468+00', '{"provider": "email", "providers": ["email"]}', '{"sub": "7a5402a0-3ff1-460c-ae39-9a0e40974558", "email": "<EMAIL>", "full_name": "BRIANMSYAMBOZA", "email_verified": true, "phone_verified": false}', NULL, '2025-09-29 22:55:59.389019+00', '2025-09-29 22:55:59.395325+00', NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, NULL, false),
	('********-0000-0000-0000-************', '9f394cc1-352a-4937-af01-271b201614dd', 'authenticated', 'authenticated', '<EMAIL>', '$2a$10$8w8GblKG1n0e.TsqppS8beX3eBGBNeWZ6A/8VqBLVgQsc3isvttb2', '2025-09-02 07:38:39.801358+00', NULL, '', NULL, '', NULL, '', '', NULL, '2025-09-29 22:58:32.803144+00', '{"provider": "email", "providers": ["email"]}', '{"email_verified": true}', NULL, '2025-09-02 07:38:39.79607+00', '2025-09-30 14:48:04.969522+00', NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, NULL, false);


--
-- Data for Name: identities; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."identities" ("provider_id", "user_id", "identity_data", "provider", "last_sign_in_at", "created_at", "updated_at", "id") VALUES
	('9f394cc1-352a-4937-af01-271b201614dd', '9f394cc1-352a-4937-af01-271b201614dd', '{"sub": "9f394cc1-352a-4937-af01-271b201614dd", "email": "<EMAIL>", "email_verified": false, "phone_verified": false}', 'email', '2025-09-02 07:38:39.798999+00', '2025-09-02 07:38:39.799051+00', '2025-09-02 07:38:39.799051+00', '13adebcd-80d1-44f1-af03-03eae3b77ad3'),
	('7a5402a0-3ff1-460c-ae39-9a0e40974558', '7a5402a0-3ff1-460c-ae39-9a0e40974558', '{"sub": "7a5402a0-3ff1-460c-ae39-9a0e40974558", "email": "<EMAIL>", "phone": null, "full_name": "BRIANMSYAMBOZA", "email_verified": false, "phone_verified": false}', 'email', '2025-09-29 22:55:59.390596+00', '2025-09-29 22:55:59.390621+00', '2025-09-29 22:55:59.390621+00', '3845d6f7-50f0-4656-ada8-eb87a8303476');


--
-- Data for Name: instances; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: oauth_clients; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: sessions; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."sessions" ("id", "user_id", "created_at", "updated_at", "factor_id", "aal", "not_after", "refreshed_at", "user_agent", "ip", "tag", "oauth_client_id") VALUES
	('fc476bc3-7943-4c02-ba16-b125e1cd3de9', '9f394cc1-352a-4937-af01-271b201614dd', '2025-09-02 07:38:42.787323+00', '2025-09-02 07:38:42.787323+00', NULL, 'aal1', NULL, NULL, 'Dart/3.7 (dart:io)', '**********', NULL, NULL),
	('d7f6d188-66e8-438f-b446-72a21920f4aa', '9f394cc1-352a-4937-af01-271b201614dd', '2025-09-02 07:39:40.667062+00', '2025-09-02 07:39:40.667062+00', NULL, 'aal1', NULL, NULL, 'Dart/3.7 (dart:io)', '**********', NULL, NULL),
	('c0ed8661-a5eb-4be1-8ae3-8f6d952f4f23', '9f394cc1-352a-4937-af01-271b201614dd', '2025-09-02 07:40:00.039961+00', '2025-09-12 20:15:42.65016+00', NULL, 'aal1', NULL, '2025-09-12 20:15:42.650109', 'Dart/3.7 (dart:io)', '**********', NULL, NULL),
	('89ea2abb-712c-405f-9f74-fff60b7ed8ce', '9f394cc1-352a-4937-af01-271b201614dd', '2025-09-29 22:58:32.803196+00', '2025-09-30 14:48:04.970703+00', NULL, 'aal1', NULL, '2025-09-30 14:48:04.970661', 'Dart/3.7 (dart:io)', '**************', NULL, NULL);


--
-- Data for Name: mfa_amr_claims; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."mfa_amr_claims" ("session_id", "created_at", "updated_at", "authentication_method", "id") VALUES
	('fc476bc3-7943-4c02-ba16-b125e1cd3de9', '2025-09-02 07:38:42.791322+00', '2025-09-02 07:38:42.791322+00', 'password', 'eb38b144-ef8f-4490-8502-7c47cb7647e2'),
	('d7f6d188-66e8-438f-b446-72a21920f4aa', '2025-09-02 07:39:40.668341+00', '2025-09-02 07:39:40.668341+00', 'password', '4417eb5c-a41c-43f9-b7a6-737ec65abd5e'),
	('c0ed8661-a5eb-4be1-8ae3-8f6d952f4f23', '2025-09-02 07:40:00.041234+00', '2025-09-02 07:40:00.041234+00', 'password', '52154ba8-9489-42c6-8d4f-7a1e2621a9f8'),
	('89ea2abb-712c-405f-9f74-fff60b7ed8ce', '2025-09-29 22:58:32.804196+00', '2025-09-29 22:58:32.804196+00', 'password', '553d1fed-9aa5-416a-a427-21507f38f47c');


--
-- Data for Name: mfa_factors; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: mfa_challenges; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: oauth_authorizations; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: oauth_consents; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: one_time_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: refresh_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."refresh_tokens" ("instance_id", "id", "token", "user_id", "revoked", "created_at", "updated_at", "parent", "session_id") VALUES
	('********-0000-0000-0000-************', 1, 'dhfhpbuxe4cl', '9f394cc1-352a-4937-af01-271b201614dd', false, '2025-09-02 07:38:42.788625+00', '2025-09-02 07:38:42.788625+00', NULL, 'fc476bc3-7943-4c02-ba16-b125e1cd3de9'),
	('********-0000-0000-0000-************', 2, 'v6udmkljki5d', '9f394cc1-352a-4937-af01-271b201614dd', false, '2025-09-02 07:39:40.667587+00', '2025-09-02 07:39:40.667587+00', NULL, 'd7f6d188-66e8-438f-b446-72a21920f4aa'),
	('********-0000-0000-0000-************', 3, 'qqvwzfnbybjs', '9f394cc1-352a-4937-af01-271b201614dd', true, '2025-09-02 07:40:00.040513+00', '2025-09-02 08:39:31.791526+00', NULL, 'c0ed8661-a5eb-4be1-8ae3-8f6d952f4f23'),
	('********-0000-0000-0000-************', 4, 'qggsxk6yc7ff', '9f394cc1-352a-4937-af01-271b201614dd', true, '2025-09-02 08:39:31.791832+00', '2025-09-02 09:39:03.203921+00', 'qqvwzfnbybjs', 'c0ed8661-a5eb-4be1-8ae3-8f6d952f4f23'),
	('********-0000-0000-0000-************', 5, '36z2ci5xi2xk', '9f394cc1-352a-4937-af01-271b201614dd', true, '2025-09-02 09:39:03.204136+00', '2025-09-02 10:38:26.733236+00', 'qggsxk6yc7ff', 'c0ed8661-a5eb-4be1-8ae3-8f6d952f4f23'),
	('********-0000-0000-0000-************', 6, 'n2zq24frqmx7', '9f394cc1-352a-4937-af01-271b201614dd', true, '2025-09-02 10:38:26.733418+00', '2025-09-02 11:58:52.341+00', '36z2ci5xi2xk', 'c0ed8661-a5eb-4be1-8ae3-8f6d952f4f23'),
	('********-0000-0000-0000-************', 7, 'qyvlz6gvopi3', '9f394cc1-352a-4937-af01-271b201614dd', true, '2025-09-02 11:58:52.341511+00', '2025-09-03 18:21:34.391419+00', 'n2zq24frqmx7', 'c0ed8661-a5eb-4be1-8ae3-8f6d952f4f23'),
	('********-0000-0000-0000-************', 8, '7dddqk6zu5dz', '9f394cc1-352a-4937-af01-271b201614dd', true, '2025-09-03 18:21:34.39665+00', '2025-09-03 19:21:03.716451+00', 'qyvlz6gvopi3', 'c0ed8661-a5eb-4be1-8ae3-8f6d952f4f23'),
	('********-0000-0000-0000-************', 9, 'sl5peuif4j57', '9f394cc1-352a-4937-af01-271b201614dd', true, '2025-09-03 19:21:03.718615+00', '2025-09-04 14:37:13.825374+00', '7dddqk6zu5dz', 'c0ed8661-a5eb-4be1-8ae3-8f6d952f4f23'),
	('********-0000-0000-0000-************', 10, 't2fakgqxxhor', '9f394cc1-352a-4937-af01-271b201614dd', true, '2025-09-04 14:37:13.826359+00', '2025-09-05 09:27:31.478378+00', 'sl5peuif4j57', 'c0ed8661-a5eb-4be1-8ae3-8f6d952f4f23'),
	('********-0000-0000-0000-************', 11, '6lkpyws6c6m5', '9f394cc1-352a-4937-af01-271b201614dd', true, '2025-09-05 09:27:31.479281+00', '2025-09-05 10:26:55.006162+00', 't2fakgqxxhor', 'c0ed8661-a5eb-4be1-8ae3-8f6d952f4f23'),
	('********-0000-0000-0000-************', 12, 'dnrhrsbbieju', '9f394cc1-352a-4937-af01-271b201614dd', true, '2025-09-05 10:26:55.006502+00', '2025-09-05 11:26:17.61854+00', '6lkpyws6c6m5', 'c0ed8661-a5eb-4be1-8ae3-8f6d952f4f23'),
	('********-0000-0000-0000-************', 13, '6tvmxmzs6man', '9f394cc1-352a-4937-af01-271b201614dd', true, '2025-09-05 11:26:17.618808+00', '2025-09-12 20:15:42.644143+00', 'dnrhrsbbieju', 'c0ed8661-a5eb-4be1-8ae3-8f6d952f4f23'),
	('********-0000-0000-0000-************', 14, '2yp6hh4p52op', '9f394cc1-352a-4937-af01-271b201614dd', false, '2025-09-12 20:15:42.646205+00', '2025-09-12 20:15:42.646205+00', '6tvmxmzs6man', 'c0ed8661-a5eb-4be1-8ae3-8f6d952f4f23'),
	('********-0000-0000-0000-************', 21, 'u6bfxcr7t7j4', '9f394cc1-352a-4937-af01-271b201614dd', true, '2025-09-29 22:58:32.803655+00', '2025-09-30 14:48:04.968445+00', NULL, '89ea2abb-712c-405f-9f74-fff60b7ed8ce'),
	('********-0000-0000-0000-************', 22, '6cnkjrrmuayz', '9f394cc1-352a-4937-af01-271b201614dd', false, '2025-09-30 14:48:04.96883+00', '2025-09-30 14:48:04.96883+00', 'u6bfxcr7t7j4', '89ea2abb-712c-405f-9f74-fff60b7ed8ce');


--
-- Data for Name: sso_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: saml_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: saml_relay_states; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: sso_domains; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."users" ("id", "email", "full_name", "avatar_url", "phone", "is_active", "created_at", "updated_at") VALUES
	('9f394cc1-352a-4937-af01-271b201614dd', '<EMAIL>', 'Demo User', NULL, NULL, true, '2025-09-02 07:39:38.197053+00', '2025-09-02 07:39:58.148944+00'),
	('7a5402a0-3ff1-460c-ae39-9a0e40974558', '<EMAIL>', 'BRIANMSYAMBOZA', NULL, NULL, true, '2025-09-29 22:55:59.417494+00', '2025-09-29 22:55:59.417494+00');


--
-- Data for Name: businesses; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."businesses" ("id", "user_id", "name", "description", "logo_url", "address", "phone", "email", "website", "tax_id", "currency", "is_default", "created_at", "updated_at") VALUES
	('24c83358-91fd-4f61-80e7-c8731bc375a3', '9f394cc1-352a-4937-af01-271b201614dd', 'My First Business', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'MWK', false, '2025-09-29 21:31:06.331213+00', '2025-09-29 21:31:06.331213+00'),
	('861bf84b-ee0f-4302-b167-a8f643ae301a', '7a5402a0-3ff1-460c-ae39-9a0e40974558', 'Personal', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'USD', true, '2025-09-30 00:55:58.636423+00', '2025-09-30 00:55:58.636492+00');


--
-- Data for Name: cashbook_templates; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."cashbook_templates" ("id", "name", "description", "icon", "is_active", "created_at") VALUES
	('c5e83115-6442-4a85-bf5e-e11b8c2aeac2', 'Personal', 'Manage household and personal finances', 'person', true, '2025-09-01 14:25:13.722273+00');


--
-- Data for Name: cashbooks; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."cashbooks" ("id", "user_id", "business_id", "template_id", "name", "description", "currency", "color", "icon", "balance", "transaction_count", "is_default", "is_active", "last_activity", "created_at", "updated_at") VALUES
	('ef3c2973-d197-4bfb-9258-f33ce6529389', '9f394cc1-352a-4937-af01-271b201614dd', '24c83358-91fd-4f61-80e7-c8731bc375a3', NULL, 'my first cashbook', 'lorem ipsum thanks again for your help and a great time 😊 and I am good too 😊 I hope', 'MWK', '0xFFF57C00', 'account_balance_wallet', 900.00, 4, false, true, '2025-09-29 22:56:42.987795+00', '2025-09-29 23:33:30.81793+00', '2025-09-29 22:56:42.987795+00'),
	('eff03b94-7388-4afd-83c5-0dce8af9f154', '7a5402a0-3ff1-460c-ae39-9a0e40974558', '24c83358-91fd-4f61-80e7-c8731bc375a3', NULL, 'my cashbook', 'No description provided', 'MWK', '0xFF7B1FA2', 'account_balance_wallet', 0.00, 0, false, true, '2025-09-30 00:57:25.243876+00', '2025-09-30 00:57:25.243876+00', '2025-09-30 00:57:25.243876+00'),
	('9ce3755e-de65-4797-b821-b8b459747439', '9f394cc1-352a-4937-af01-271b201614dd', '24c83358-91fd-4f61-80e7-c8731bc375a3', NULL, 'regbjm', 'gb', 'MWK', '0xFF455A64', 'account_balance_wallet', 0.00, 0, false, false, '2025-09-30 01:09:59.094178+00', '2025-09-30 01:09:59.094178+00', '2025-09-29 23:10:04.64889+00');


--
-- Data for Name: categories; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."categories" ("id", "name", "type", "icon", "color", "parent_id", "sort_order", "is_active", "created_at") VALUES
	('323e1561-483e-4f00-905d-9ff8375a4fe1', 'Sales Revenue', 'income', 'trending_up', NULL, NULL, 1, true, '2025-09-02 09:18:12.565973+00'),
	('10933dfb-94d3-45f0-a231-7c215498a98a', 'Product Returns', 'expense', 'keyboard_return', NULL, NULL, 2, true, '2025-09-02 09:18:12.565973+00'),
	('a1726031-22ad-46ad-adf3-4bfbd9085ccb', 'Inventory Purchase', 'expense', 'inventory', NULL, NULL, 3, true, '2025-09-02 09:18:12.565973+00'),
	('606ecdb8-da5d-4807-80eb-8511dd63d7ee', 'Store Rent', 'expense', 'store', NULL, NULL, 4, true, '2025-09-02 09:18:12.565973+00'),
	('0c82fbd9-adb3-42ae-b15e-20099e7a66cc', 'Utilities', 'expense', 'flash_on', NULL, NULL, 5, true, '2025-09-02 09:18:12.565973+00'),
	('01839841-5207-45bf-93fb-19077d2c7367', 'Staff Salaries', 'expense', 'people', NULL, NULL, 6, true, '2025-09-02 09:18:12.565973+00');


--
-- Data for Name: budgets; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: business_invitations; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."business_invitations" ("business_id", "email", "invited_by", "invitation_token", "accepted_at", "id", "role", "status", "expires_at", "created_at", "updated_at") VALUES
	('24c83358-91fd-4f61-80e7-c8731bc375a3', '<EMAIL>', '9f394cc1-352a-4937-af01-271b201614dd', '9QEthPTtY33Lqf8V7oEVISkAZPMvk+3ZFXLky8G+rEU=', '2025-09-30 00:56:07.245982+00', '3f49580f-0ff0-4f12-9aa7-d3ddb337f0f6', 'partner', 'accepted', '2025-10-06 22:35:33.174884+00', '2025-09-29 22:35:33.174884+00', '2025-09-30 00:56:07.245982+00');


--
-- Data for Name: business_members; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."business_members" ("business_id", "user_id", "invited_by", "id", "role", "joined_at", "is_active", "created_at", "updated_at") VALUES
	('24c83358-91fd-4f61-80e7-c8731bc375a3', '9f394cc1-352a-4937-af01-271b201614dd', '9f394cc1-352a-4937-af01-271b201614dd', '9c988036-ff11-4277-a9b7-8e4c677eaef9', 'admin', '2025-09-29 21:33:32.178471+00', true, '2025-09-29 21:33:32.178471+00', '2025-09-29 21:33:32.178471+00'),
	('24c83358-91fd-4f61-80e7-c8731bc375a3', '7a5402a0-3ff1-460c-ae39-9a0e40974558', NULL, '3312aa22-1055-48ee-b20c-de9668ed6391', 'partner', '2025-09-30 00:56:07.245982+00', true, '2025-09-30 00:56:07.245982+00', '2025-09-30 00:56:07.245982+00'),
	('24c83358-91fd-4f61-80e7-c8731bc375a3', '7a5402a0-3ff1-460c-ae39-9a0e40974558', '7a5402a0-3ff1-460c-ae39-9a0e40974558', '18600f3f-5136-45cd-b2f0-3d1203c6a262', 'admin', '2025-09-29 22:57:26.310871+00', true, '2025-09-29 22:57:26.310871+00', '2025-09-29 22:57:26.310871+00'),
	('24c83358-91fd-4f61-80e7-c8731bc375a3', '9f394cc1-352a-4937-af01-271b201614dd', '9f394cc1-352a-4937-af01-271b201614dd', '8dabfbad-009a-4970-8926-81520629cc1f', 'admin', '2025-09-29 23:10:00.250455+00', true, '2025-09-29 23:10:00.250455+00', '2025-09-29 23:10:00.250455+00');


--
-- Data for Name: cashbook_shares; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: member_cashbook_permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."member_cashbook_permissions" ("business_member_id", "cashbook_id", "granted_by", "id", "permission", "created_at", "updated_at", "user_id") VALUES
	('9c988036-ff11-4277-a9b7-8e4c677eaef9', 'ef3c2973-d197-4bfb-9258-f33ce6529389', '9f394cc1-352a-4937-af01-271b201614dd', '2415de13-e908-4fa5-be95-622df34e91d0', 'write', '2025-09-29 21:33:32.190714+00', '2025-09-29 21:33:32.190714+00', '9f394cc1-352a-4937-af01-271b201614dd'),
	('9c988036-ff11-4277-a9b7-8e4c677eaef9', 'ef3c2973-d197-4bfb-9258-f33ce6529389', NULL, '60047ce9-5286-4548-9073-53887fc1ecae', 'write', '2025-09-30 00:56:07.245982+00', '2025-09-30 00:56:07.245982+00', '7a5402a0-3ff1-460c-ae39-9a0e40974558'),
	('18600f3f-5136-45cd-b2f0-3d1203c6a262', 'eff03b94-7388-4afd-83c5-0dce8af9f154', '7a5402a0-3ff1-460c-ae39-9a0e40974558', 'f404d3d1-ec07-463f-b273-b070db48b9b4', 'write', '2025-09-29 22:57:26.322102+00', '2025-09-29 22:57:26.322102+00', '7a5402a0-3ff1-460c-ae39-9a0e40974558'),
	('8dabfbad-009a-4970-8926-81520629cc1f', '9ce3755e-de65-4797-b821-b8b459747439', '9f394cc1-352a-4937-af01-271b201614dd', '70f43ed9-7aad-4845-8340-f063493d7267', 'write', '2025-09-29 23:10:00.759576+00', '2025-09-29 23:10:00.759576+00', '9f394cc1-352a-4937-af01-271b201614dd');


--
-- Data for Name: notification_settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."notification_settings" ("user_id", "id", "local_notifications_enabled", "push_notifications_enabled", "email_notifications_enabled", "transaction_notifications", "invitation_notifications", "broadcast_notifications", "reminder_notifications", "system_notifications", "sound_enabled", "vibration_enabled", "quiet_hours_enabled", "quiet_hours_start", "quiet_hours_end", "created_at", "updated_at") VALUES
	('9f394cc1-352a-4937-af01-271b201614dd', 'abc57808-2b39-4e7a-8e03-262a214c8092', true, true, true, true, true, true, true, true, true, true, false, NULL, NULL, '2025-09-29 22:36:34.424362+00', '2025-09-29 22:36:34.424362+00'),
	('7a5402a0-3ff1-460c-ae39-9a0e40974558', '20044e16-012e-4235-b6f9-8e70d09168ac', true, true, true, true, true, true, true, true, true, true, false, NULL, NULL, '2025-09-30 00:56:51.662561+00', '2025-09-30 00:56:51.662561+00');


--
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: payment_modes; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."payment_modes" ("id", "name", "icon", "color", "is_active", "sort_order", "created_at") VALUES
	('411035a9-0bcf-46b8-8228-28db91f0d494', 'Cash', 'money', NULL, true, 1, '2025-09-01 14:26:08.769267+00'),
	('8672fefe-8cc6-4f91-957c-5ba3fd8cccb7', 'Credit Card', 'credit_card', NULL, true, 2, '2025-09-01 14:26:08.769267+00'),
	('512f801b-bbe8-41aa-98a9-d4e9ac9e0fdc', 'Debit Card', 'payment', NULL, true, 3, '2025-09-01 14:26:08.769267+00'),
	('4ac39f39-4c26-4db3-af4a-2284945d00e9', 'Bank Transfer', 'account_balance', NULL, true, 4, '2025-09-01 14:26:08.769267+00'),
	('5bf2849e-3386-476f-bebf-34071ff6e66b', 'Digital Wallet', 'account_balance_wallet', NULL, true, 5, '2025-09-01 14:26:08.769267+00'),
	('68f64224-3254-45d7-95dd-bca57ae702b9', 'Check', 'receipt', NULL, true, 6, '2025-09-01 14:26:08.769267+00'),
	('2dc4ecb8-d866-485e-b96f-5a3cad0e01ae', 'PayPal', 'paypal', NULL, true, 7, '2025-09-01 14:26:08.769267+00'),
	('b4e1e218-0f2a-414c-8c8c-f32b164c69c1', 'Cryptocurrency', 'currency_bitcoin', NULL, true, 8, '2025-09-01 14:26:08.769267+00'),
	('539c658f-a9f8-472c-b695-ade13e87a7bc', 'Mobile Payment', 'phone_android', NULL, true, 9, '2025-09-01 14:26:08.769267+00'),
	('dc6e091a-d9f7-4497-885e-29970ce463d2', 'Wire Transfer', 'swap_horiz', NULL, true, 10, '2025-09-01 14:26:08.769267+00');


--
-- Data for Name: recurring_transactions; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: transactions; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."transactions" ("id", "cashbook_id", "user_id", "category_id", "payment_mode_id", "type", "amount", "description", "notes", "reference_number", "transaction_date", "created_at", "updated_at") VALUES
	('758d5022-5d96-40ec-9883-c4337f5f7454', 'ef3c2973-d197-4bfb-9258-f33ce6529389', '9f394cc1-352a-4937-af01-271b201614dd', '323e1561-483e-4f00-905d-9ff8375a4fe1', '411035a9-0bcf-46b8-8228-28db91f0d494', 'income', 1000.00, 'first transaction', NULL, NULL, '2025-09-29', '2025-09-29 23:55:19.107763+00', '2025-09-29 23:55:19.107763+00'),
	('948f21c0-f4fc-4c5d-b5ad-609c14dcfd3d', 'ef3c2973-d197-4bfb-9258-f33ce6529389', '9f394cc1-352a-4937-af01-271b201614dd', '323e1561-483e-4f00-905d-9ff8375a4fe1', '411035a9-0bcf-46b8-8228-28db91f0d494', 'income', 200.00, 'transaction two', NULL, NULL, '2025-09-29', '2025-09-29 23:58:36.646819+00', '2025-09-29 23:58:36.646819+00'),
	('bd2bdcb0-915a-4251-8cd4-292c67bc9290', 'ef3c2973-d197-4bfb-9258-f33ce6529389', '9f394cc1-352a-4937-af01-271b201614dd', '606ecdb8-da5d-4807-80eb-8511dd63d7ee', '8672fefe-8cc6-4f91-957c-5ba3fd8cccb7', 'expense', 200.00, 'deduction', NULL, NULL, '2025-09-29', '2025-09-29 23:58:58.567939+00', '2025-09-29 23:58:58.567939+00'),
	('7f0798db-28b6-4572-800f-a6e8c6c37fc5', 'ef3c2973-d197-4bfb-9258-f33ce6529389', '7a5402a0-3ff1-460c-ae39-9a0e40974558', '606ecdb8-da5d-4807-80eb-8511dd63d7ee', '411035a9-0bcf-46b8-8228-28db91f0d494', 'expense', 100.00, 'desc', NULL, NULL, '2025-09-30', '2025-09-30 00:56:42.102872+00', '2025-09-30 00:56:42.102872+00');


--
-- Data for Name: transaction_attachments; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: user_preferences; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: buckets; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: buckets_analytics; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: iceberg_namespaces; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: iceberg_tables; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: objects; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: prefixes; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: s3_multipart_uploads; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: s3_multipart_uploads_parts; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: hooks; Type: TABLE DATA; Schema: supabase_functions; Owner: supabase_functions_admin
--



--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE SET; Schema: auth; Owner: supabase_auth_admin
--

SELECT pg_catalog.setval('"auth"."refresh_tokens_id_seq"', 22, true);


--
-- Name: hooks_id_seq; Type: SEQUENCE SET; Schema: supabase_functions; Owner: supabase_functions_admin
--

SELECT pg_catalog.setval('"supabase_functions"."hooks_id_seq"', 1, false);


--
-- PostgreSQL database dump complete
--

-- \unrestrict znqSJW5lvpyKq3afXi8GURLTlgLS2xyOcnm6ndjUuFt1lDFEXUa4rwLM1OGRXn0

RESET ALL;
